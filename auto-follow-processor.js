/**
 * 自动跟进处理器
 * 集成API客户端实现完整的自动跟进流程
 */

class AutoFollowProcessor {
  constructor() {
    this.apiClient = window.apiClient || new AutoFollowUpAPI();
    this.isRunning = false;
    this.processedLeads = new Set();
    this.config = {
      batchSize: 5,           // 每批处理数量
      delayBetweenRequests: 2000,  // 请求间隔(ms)
      maxRetries: 3,          // 最大重试次数
      autoRemark: true,       // 自动生成备注
      followUpMessages: [     // 跟进消息模板
        "客户您好，我是您的专属顾问，有什么可以帮助您的吗？",
        "感谢您对我们产品的关注，请问您还有其他疑问吗？",
        "您好，根据您的需求，我为您推荐几款热门车型",
        "客户您好，我们最近有优惠活动，您可以了解一下",
        "感谢您的咨询，请问您方便到店详细了解吗？"
      ]
    };
  }

  /**
   * 开始自动跟进流程
   */
  async startAutoFollow(options = {}) {
    if (this.isRunning) {
      this.log('自动跟进已在运行中', null, 'warn');
      return;
    }

    this.isRunning = true;
    this.config = { ...this.config, ...options };
    
    try {
      this.log('🚀 开始自动跟进流程');
      
      // 1. 获取待跟进客户列表
      const leadList = await this.getLeadsToFollow();
      
      if (!leadList || !leadList.data || !leadList.data.pageData) {
        this.log('未找到待跟进客户', null, 'warn');
        return;
      }

      const leads = leadList.data.pageData;
      this.log(`📋 找到 ${leads.length} 个待跟进客户`);

      // 2. 批量处理客户
      await this.processBatch(leads);
      
      this.log('✅ 自动跟进流程完成');
      
    } catch (error) {
      this.log('❌ 自动跟进流程出错', error, 'error');
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 获取待跟进客户列表
   */
  async getLeadsToFollow() {
    try {
      const params = {
        state: '201',  // 再次待跟进状态
        pageSize: this.config.batchSize
      };
      
      return await this.apiClient.getLeadList(params);
    } catch (error) {
      this.log('获取客户列表失败', error, 'error');
      throw error;
    }
  }

  /**
   * 批量处理客户
   */
  async processBatch(leads) {
    for (let i = 0; i < leads.length; i++) {
      const lead = leads[i];
      
      if (this.processedLeads.has(lead.leadId)) {
        this.log(`⏭️ 跳过已处理客户: ${lead.guestName} (${lead.leadId})`);
        continue;
      }

      try {
        this.log(`🔄 处理客户 ${i + 1}/${leads.length}: ${lead.guestName} (${lead.leadId})`);
        
        // 处理单个客户
        await this.processSingleLead(lead);
        
        // 标记为已处理
        this.processedLeads.add(lead.leadId);
        
        // 请求间隔
        if (i < leads.length - 1) {
          await this.delay(this.config.delayBetweenRequests);
        }
        
      } catch (error) {
        this.log(`❌ 处理客户失败: ${lead.guestName}`, error, 'error');
      }
    }
  }

  /**
   * 处理单个客户
   */
  async processSingleLead(lead) {
    let retries = 0;
    
    while (retries < this.config.maxRetries) {
      try {
        // 1. 获取客户详情
        const leadDetail = await this.apiClient.getLeadDetail(lead.leadId);
        
        if (!leadDetail || !leadDetail.data) {
          throw new Error('获取客户详情失败');
        }

        // 2. 构建跟进数据
        const followUpData = this.buildFollowUpData(leadDetail.data);
        
        // 3. 提交跟进记录
        const result = await this.apiClient.submitFollowUp(followUpData);
        
        if (result && result.code === '000000') {
          this.log(`✅ 客户跟进成功: ${lead.guestName}`);
          return result;
        } else {
          throw new Error(`提交失败: ${result?.description || '未知错误'}`);
        }
        
      } catch (error) {
        retries++;
        this.log(`⚠️ 第${retries}次尝试失败: ${error.message}`, null, 'warn');
        
        if (retries >= this.config.maxRetries) {
          throw error;
        }
        
        // 重试前等待
        await this.delay(1000 * retries);
      }
    }
  }

  /**
   * 构建跟进数据
   */
  buildFollowUpData(leadData) {
    const now = new Date();
    const nextFollowTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
    
    return {
      leadId: leadData.leadId,
      userName: leadData.userName,
      userMobile: leadData.userMobile,
      intentionSeries: `${leadData.intentionSeriesId}_${leadData.intentionSeriesName}`,
      intentionSeriesId: leadData.intentionSeriesId,
      intentionSeriesName: leadData.intentionSeriesName,
      consultant: leadData.consultant,
      consultantId: leadData.consultantId,
      remark: this.generateRemark(leadData),
      nextFollowTime: this.apiClient.formatDateTime(nextFollowTime),
      buyCarDate: this.apiClient.formatDateTime(now),
      level: "2_B（30天内跟进）_720",
      levelId: "2",
      levelName: "B（30天内跟进）"
    };
  }

  /**
   * 生成跟进备注
   */
  generateRemark(leadData) {
    if (!this.config.autoRemark) {
      return "自动跟进记录";
    }

    const messages = this.config.followUpMessages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    const timestamp = new Date().toLocaleString();
    const carInfo = leadData.intentionSeriesName ? `关注车型：${leadData.intentionSeriesName}` : '';
    
    return `${randomMessage} ${carInfo} [自动跟进 ${timestamp}]`;
  }

  /**
   * 停止自动跟进
   */
  stop() {
    this.isRunning = false;
    this.log('🛑 自动跟进已停止');
  }

  /**
   * 获取处理统计
   */
  getStats() {
    return {
      isRunning: this.isRunning,
      processedCount: this.processedLeads.size,
      processedLeads: Array.from(this.processedLeads)
    };
  }

  /**
   * 重置处理记录
   */
  reset() {
    this.processedLeads.clear();
    this.log('🔄 处理记录已重置');
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 日志输出
   */
  log(message, data = null, level = 'info') {
    const timestamp = new Date().toLocaleString();
    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '✅';
    
    console.log(`${prefix} [自动跟进] ${timestamp} - ${message}`);
    if (data) {
      console.log(data);
    }

    // 发送到插件界面
    if (window.Statistics) {
      window.Statistics.addLog(message, level);
    }
  }

  /**
   * 设置配置
   */
  setConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.log('⚙️ 配置已更新', this.config);
  }
}

// 导出处理器
window.AutoFollowProcessor = AutoFollowProcessor;

// 创建全局实例
window.autoFollowProcessor = new AutoFollowProcessor();

console.log('✅ [自动跟进处理器] 已加载完成');
