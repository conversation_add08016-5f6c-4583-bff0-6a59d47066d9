let isRunning = false;
let settings = null;

// 发送初始化消息
console.log('[自动跟进助手] Content script已加载');
chrome.runtime.sendMessage({
  action: 'contentScriptLoaded',
  message: 'Content script已加载'
});

// 日志和统计管理
const Statistics = {
  totalAttempts: 0,
  successCount: 0,
  failureCount: 0,
  totalSuccess: 0,
  historyTotal: 0,
  buttonFailures: 0,    // 跟进按钮未找到的次数
  isSleeping: false,    // 休眠状态
  sleepEndTime: null,   // 休眠结束时间
  startTime: null,
  logs: [],
  maxLogs: 100000,
  sleepTimer: null,
  skipCurrentRecord: false, // 添加跳过标记
  currentIndex: 0,  // 添加当前位置记录

  start(reset = false) {
    if (reset) {
      this.reset();
    } else {
      this.startTime = new Date();
      this.successCount = 0;
      this.failureCount = 0;
      this.totalAttempts = 0;
      this.buttonFailures = 0;
      this.isSleeping = false;
      this.sleepEndTime = null;
      this.logs = [];
      this.loadHistory();
    }
    this.updatePopup();
    this.addLog('开始自动化流程', 'info');
  },

  loadHistory() {
    const savedTotal = localStorage.getItem('historyTotal');
    const savedSuccess = localStorage.getItem('totalSuccess');
    this.historyTotal = savedTotal ? parseInt(savedTotal) : 0;
    this.totalSuccess = savedSuccess ? parseInt(savedSuccess) : 0;
  },

  saveHistory() {
    localStorage.setItem('historyTotal', this.historyTotal.toString());
    localStorage.setItem('totalSuccess', this.totalSuccess.toString());
  },

  addSuccess() {
    this.successCount++;
    this.totalSuccess++;  // 增加历史成功数
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('✅ 操作成功', 'success');
  },

  addFailure(isButtonFailure = false) {
    this.failureCount++;
    if (isButtonFailure) {
      this.buttonFailures++;
    }
    this.historyTotal++;  // 增加历史总数
    this.saveHistory();   // 保存历史数据
    this.updatePopup();   // 更新显示
    this.addLog('❌ 操作失败', 'error');
  },

  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 根据类型添加不同的前缀
    let prefix = '';
    switch(type) {
      case 'success': prefix = '✅ '; break;
      case 'error': prefix = '❌ '; break;
      case 'warning': prefix = '⚠️ '; break;
      case 'info': prefix = 'ℹ️ '; break;
    }

    const logMessage = `${prefix}${message}`;
    
    // 发送日志到popup
    try {
      chrome.runtime.sendMessage({
        action: 'log',
        data: {
          timestamp,
          message: logMessage,
          type
        }
      });
    } catch (error) {
      console.error('发送日志失败:', error);
    }

    // 保存到本地日志
    this.logs.unshift({
      time: timestamp,
      message: logMessage,
      type: type
    });

    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // 保存日志到storage
    chrome.storage.local.get(['savedLogs'], (data) => {
      const logs = data.savedLogs || [];
      logs.unshift({
        time: timestamp,
        message: logMessage,
        type: type
      });

      if (logs.length > 100) {
        logs.pop();
      }

      chrome.storage.local.set({ savedLogs: logs });
    });

    console.log(`[自动跟进助手] ${timestamp} ${logMessage}`);
    this.updatePopup();
  },

  updatePopup() {
    // 发送完整的统计数据到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'updateStats',
        stats: {
          ...this.getStats(),
          currentSuccess: this.successCount,
          currentFail: this.failureCount,
          totalSuccess: this.totalSuccess,
          historyTotal: this.historyTotal
        }
      });
    } catch (error) {
      console.error('发送统计更新失败:', error);
    }
  },

  getStats() {
    return {
      successCount: this.successCount,
      failureCount: this.failureCount,
      buttonFailures: this.buttonFailures,
      totalSuccess: this.totalSuccess,
      historyTotal: this.historyTotal,
      logs: this.logs,
      status: {
        isRunning,
        isSleeping: this.isSleeping,
        sleepEndTime: this.sleepEndTime
      }
    };
  },

  startSleepTimer() {
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
    }

    this.sleepTimer = setInterval(() => {
      // 如果已停止运行，立即清除定时器
      if (!isRunning || !this.isSleeping || !this.sleepEndTime) {
        clearInterval(this.sleepTimer);
        this.sleepTimer = null;
        this.isSleeping = false;
        this.sleepEndTime = null;
        this.updatePopup();
        return;
      }

      // 计算剩余时间（精确到秒）
      const now = new Date();
      const remainingMs = new Date(this.sleepEndTime) - now;
      const remainingMinutes = Math.floor(remainingMs / 60000);
      const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
      
      // 格式化时间显示 (确保分钟和秒数都是两位数)
      const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

      // 发送实时更新消息
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: true,
          timeDisplay,
          remainingMs,
          sleepEndTime: this.sleepEndTime,
          isRunning: isRunning
        }
      });

      if (remainingMs <= 0) {
        this.clearSleepMode();
        this.addLog('休眠结束，继续运行', 'info');
      }

      this.updatePopup();
    }, 1000);
  },

  setSleepMode(minutes) {
    this.isSleeping = true;
    this.sleepEndTime = new Date(Date.now() + minutes * 60 * 1000);
    
    // 立即发送一次状态更新
    const remainingMs = this.sleepEndTime - new Date();
    const remainingMinutes = Math.floor(remainingMs / 60000);
    const remainingSeconds = Math.floor((remainingMs % 60000) / 1000);
    const timeDisplay = `${String(remainingMinutes).padStart(2, '0')}分${String(remainingSeconds).padStart(2, '0')}秒`;

    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: true,
        timeDisplay,
        remainingMs,
        sleepEndTime: this.sleepEndTime,
        isRunning: isRunning
      }
    });
    
    this.startSleepTimer();
    this.updatePopup();
    this.addLog(`💤 进入休眠模式，${minutes}分钟后自动继续`, 'info');
  },

  clearSleepMode() {
    // 先清除定时器
    if (this.sleepTimer) {
      clearInterval(this.sleepTimer);
      this.sleepTimer = null;
    }

    this.isSleeping = false;
    this.sleepEndTime = null;

    // 发送休眠结束状态
    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: false,
        timeDisplay: '',
        remainingMs: 0,
        isRunning: isRunning // 使用当前运行状态
      }
    });
    
    this.updatePopup();
  },

  // 修改复位方法
  reset() {
    // 直接复位所有数据
    this.successCount = 0;
    this.failureCount = 0;
    this.totalAttempts = 0;
    this.buttonFailures = 0;
    this.isSleeping = false;
    this.sleepEndTime = null;
    this.logs = [];
    this.totalSuccess = 0;
    this.historyTotal = 0;
    this.currentIndex = 0;

    // 清除本地存储
    localStorage.removeItem('historyTotal');
    localStorage.removeItem('totalSuccess');

    // 更新显示
    this.updatePopup();
    this.addLog('🔄 统计数据已复位', 'info');

    // 发送复位消息到 popup
    try {
      chrome.runtime.sendMessage({
        action: 'statsReset',
        stats: this.getStats()
      });
    } catch (error) {
      console.error('发送复位消息失败:', error);
    }
  },

  moveToNext() {
    this.currentIndex++;
    this.addLog(`📍 移动到第 ${this.currentIndex + 1} 条记录`);
  },
};

// 修改查找跟进按钮的函数
async function findFollowButton() {
  try {
    // 获取所有跟进按钮
    const buttons = Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent.includes('跟进') && !btn.disabled && 
      window.getComputedStyle(btn).display !== 'none'
    );
    
    Statistics.addLog(`找到 ${buttons.length} 个跟进按钮，当前索引: ${Statistics.currentIndex}`);
    





    
    // 如果没有找到按钮，返回 null
    if (!buttons.length) {
      Statistics.addLog('❌ 未找到跟进按钮，尝试点击重置按钮', 'error');
      // 查找并点击重置按钮
      const resetButton = document.querySelector("#app > section > section > main > section > div > form > div:nth-child(1) > div:nth-child(4) > div > div > button:nth-child(2)");
      if (resetButton) {
        await clickButton(resetButton);
        Statistics.addLog('🖱️ 点击重置按钮', 'info');
        // 等待一段时间后重试
        await wait(getOperationWaitTime());
      } else {
        Statistics.addLog('❌ 未找到重置按钮', 'error');
      }
      
      
      
      
      
      return null;
    }
    
    // 如果当前索引超出按钮数量，重置为0
    if (Statistics.currentIndex >= buttons.length) {
      Statistics.currentIndex = 0;
      Statistics.addLog('索引超出范围，重置为0');
    }
    
    // 返回当前索引对应的按钮
    const targetButton = buttons[Statistics.currentIndex];
    if (targetButton) {
      Statistics.addLog(`选择第 ${Statistics.currentIndex + 1} 个跟进按钮`);
      return targetButton;
    }
    
    return null;
  } catch (error) {
    Statistics.addLog(`❌ 查找跟进按钮失败: ${error.message}`, 'error');
    return null;
  }
}

// 处理升级通知弹窗
async function handleUpgradeDialog() {
  await wait(getOperationWaitTime() * 2);
  const dialog = document.querySelector('.el-dialog__wrapper');
  if (!dialog) return;

  const titleEl = dialog.querySelector('.el-dialog__title');
  if (!titleEl || titleEl.textContent !== '升级通知') return;

  const buttons = dialog.querySelectorAll('button');
  const closeButton = Array.from(buttons).find(btn => 
    btn.textContent.trim() === '关闭' || 
    btn.textContent.includes('关闭')
  );

  if (closeButton) {
    await clickButton(closeButton);
    await wait(getOperationWaitTime() * 2);
  }
}

// 查找文本输入框
async function findTextInput() {
  let attempts = 0;
  const maxAttempts = 6;

  while (attempts < maxAttempts) {
    const textarea = document.querySelector('textarea.el-textarea__inner');
    if (textarea) {
      const style = window.getComputedStyle(textarea);
      if (style.display !== 'none') {
        // 确保输入框在对话框内可见
        const dialogBody = document.querySelector('.el-dialog__body');
        if (dialogBody) {
          const textareaRect = textarea.getBoundingClientRect();
          const dialogRect = dialogBody.getBoundingClientRect();
          
          // 检查输入框是否在对话框可视区域内
          if (textareaRect.top < dialogRect.top || textareaRect.bottom > dialogRect.bottom) {
            // 滚动到输入框位置
            dialogBody.scrollTo({
              top: dialogBody.scrollTop + (textareaRect.top - dialogRect.top) - (dialogRect.height / 2) + (textareaRect.height / 2),
              behavior: 'smooth'
            });
            await wait(getOperationWaitTime());
          }
        }
        return textarea;
      }    // 4. 检测窗口是否自动关闭
      const maxWaitTime = 4000;
      const startTime = Date.now();
      const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      const initialCount = initialDialogs.length;
      let isAutoClose = false;  // 添加标记，记录是否是自动关闭
      
      while (Date.now() - startTime < maxWaitTime) {
        const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
        if (currentDialogs.length < initialCount) {
          Statistics.addLog('✅ 检测到窗口已自动关闭');
          Statistics.addSuccess(); // 自动关闭，记为成功
          isAutoClose = true;  // 标记为自动关闭
          return true;
        }
        // 移除不必要的等待
      }

      // 5. 如果窗口未自动关闭，处理选择框
      if (!isAutoClose) {  // 只有在非自动关闭时才继续
        Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');
  
        // 7. 再次检测窗口是否自动关闭
        const secondStartTime = Date.now();
        while (Date.now() - secondStartTime < maxWaitTime) {
          const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
          if (currentDialogs.length < initialCount) {
            Statistics.addLog('✅ 检测到窗口已自动关闭');
            Statistics.addSuccess(); // 自动关闭，记为成功
            isAutoClose = true;  // 标记为自动关闭
            return true;
          }
          // 移除不必要的等待
        }
  
        // 8. 如果还是未自动关闭，尝试手动关闭（这种情况算作失败）
        if (!isAutoClose) {  // 确保只在非自动关闭时执行
          Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');
          Statistics.addFailure(); // 在这里只记录一次失败
          
          const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") || 
                             document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");
          
          if (closeButton) {
            Statistics.addLog('🖱️ 点击关闭按钮');
            closeButton.click();
            await wait(getOperationWaitTime());
            
            const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
            if (finalDialogs.length < initialCount) {
              Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
              Statistics.currentIndex++;
              Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
              await wait(getOperationWaitTime());
              return true;
            }
          }

          Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
          Statistics.currentIndex++;
          Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
          await wait(getOperationWaitTime());
        }
      }
      return true;
    }
    await wait(getOperationWaitTime());
    attempts++;
  }
  throw new Error('未找到文本输入框');
}

// 修改输入文本函数，添加智能表单填充功能
async function inputText(textInput) {
  try {
    // 1. 直接输入内容
    const dialogBody = document.querySelector('.el-dialog__body');
    if (dialogBody) {
    }

    // 2. 获取随机回复内容并输入文本
    const message = getRandomMessage();
    if (!message) {
      Statistics.addLog('❌ 无法获取有效消息', 'error');
      return false;
    }

    textInput.value = message;
    textInput.dispatchEvent(new InputEvent('input', { bubbles: true }));
    textInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(getOperationWaitTime());
    Statistics.addLog('✍️ 文本输入完成');

    // 3. 智能表单自动填充（如果启用）
    if (settings && settings.autoFillForm) {
      await autoFillForm();
    } else {
      Statistics.addLog('⚠️ 智能表单填充已禁用，跳过');
    }

    // 4. 第一次点击保存按钮

    // 移除保存前等待，直接执行

    const saveButton = Array.from(document.querySelectorAll('button'))
      .find(btn => btn.textContent.includes('保存'));

    if (!saveButton) {
      Statistics.addLog('❌ 未找到保存按钮', 'error');
      return false;
    }

    Statistics.addLog('🖱️ 点击保存按钮');
    saveButton.click();
    await wait(1); // 最小必要等待

    // 4. 检测窗口是否自动关闭
    const maxWaitTime = 5000;
    const startTime = Date.now();
    const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const initialCount = initialDialogs.length;

    // 使用智能检测窗口关闭
    const windowClosed = await smartDetectWindowClosed(initialCount, 100);

    if (windowClosed) {
      Statistics.addLog('✅ 检测到窗口已自动关闭');
      return true;
    }

    // 5. 如果窗口未自动关闭，直接再次点击保存
    Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');

    // 6. 再次点击保存按钮
    Statistics.addLog('🖱️ 再次点击保存按钮');
    saveButton.click();
    await wait(getOperationWaitTime());

    // 7. 再次检测窗口是否自动关闭
    const secondStartTime = Date.now();
    while (Date.now() - secondStartTime < maxWaitTime) {
      const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (currentDialogs.length < initialCount) {
        Statistics.addLog('✅ 检测到窗口已自动关闭');
        return true;
      }
      await wait(1); // 最小检测间隔
    }

    // 8. 如果还是未自动关闭，尝试手动关闭
    Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');

    const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") ||
                       document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");

    if (closeButton) {
      Statistics.addLog('🖱️ 点击关闭按钮');
      closeButton.click();
      await wait(getOperationWaitTime());

      const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      if (finalDialogs.length < initialCount) {
        Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
        Statistics.currentIndex++;
        Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
        await wait(getOperationWaitTime() * 2);
        return true;
      }
    }

    Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
    Statistics.currentIndex++;
    Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
    await wait(getOperationWaitTime() * 2);
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 操作失败: ${error.message}`, 'error');
    throw error;
  }
}

async function performSingleFollow() {
  try {
    // 首先检查是否需要跳过当前记录
    if (Statistics.skipCurrentRecord) {
      Statistics.skipCurrentRecord = false;
      Statistics.moveToNext(); // 移动到下一条记录
      Statistics.addLog('⏭️ 跳过当前记录，继续处理下一条');
      await wait(1); // 移除不必要的跳过等待
      return true;
    }

    // 查找跟进按钮
    const followButton = await findFollowButton();
    if (!followButton) {
      Statistics.addFailure(true);
      Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
      // 如果当前索引不为0，重置索引
      if (Statistics.currentIndex > 0) {
        Statistics.currentIndex = 0;
        Statistics.addLog('🔄 重置到第一条记录');
      }
      await wait(10000);
      return false;
    }
    
    Statistics.addLog('找到跟进按钮，准备点击');
    await clickButton(followButton);
    Statistics.addLog('已点击跟进按钮');

    await handleUpgradeDialog();

    const textInput = await findTextInput();
    Statistics.addLog('找到文本输入框');

    // 继续正常的处理流程
    const result = await inputText(textInput);

    if (result) {
      // 只有在没有设置skipCurrentRecord时才计算成功
      if (!Statistics.skipCurrentRecord) {
        Statistics.addSuccess();
        Statistics.addLog('本次跟进操作完成', 'success');
      }

      const waitTime = getRandomWaitTime();
      const waitTimeDisplay = waitTime < 1 ?
        `${(waitTime * 1000).toFixed(0)}毫秒` :
        `${waitTime}秒`;
      Statistics.addLog(`⏳ 等待${waitTimeDisplay}后开始下一轮...`);
      await wait(waitTime);
      return true;
    } else {
      Statistics.addFailure();
      Statistics.addLog('❌ 跟进操作失败', 'error');
      await wait(10000);
      return false;
    }
  } catch (error) {
    Statistics.addFailure();
    Statistics.addLog(`❌ ${error.message}`, 'error');
    await wait(10000);
    return false;
  }
}

async function findTextInput() {
  let attempts = 0;
  const maxAttempts = 6;

  while (attempts < maxAttempts) {
    const textarea = document.querySelector('textarea.el-textarea__inner');
    if (textarea) {
      const style = window.getComputedStyle(textarea);
      if (style.display !== 'none') {
        // 确保输入框在对话框内可见
        const dialogBody = document.querySelector('.el-dialog__body');
        if (dialogBody) {
          const textareaRect = textarea.getBoundingClientRect();
          const dialogRect = dialogBody.getBoundingClientRect();
          
          // 检查输入框是否在对话框可视区域内
          if (textareaRect.top < dialogRect.top || textareaRect.bottom > dialogRect.bottom) {
            // 滚动到输入框位置
            dialogBody.scrollTo({
              top: dialogBody.scrollTop + (textareaRect.top - dialogRect.top) - (dialogRect.height / 2) + (textareaRect.height / 2),
              behavior: 'smooth'
            });
            // 移除滚动等待，现代浏览器滚动很快
          }
        }
        return textarea;
      }    // 4. 检测窗口是否自动关闭
      const maxWaitTime = 4000;
      const startTime = Date.now();
      const initialDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
      const initialCount = initialDialogs.length;
      let isAutoClose = false;  // 添加标记，记录是否是自动关闭
      
      while (Date.now() - startTime < maxWaitTime) {
        const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
        if (currentDialogs.length < initialCount) {
          Statistics.addLog('✅ 检测到窗口已自动关闭');
          Statistics.addSuccess(); // 自动关闭，记为成功
          isAutoClose = true;  // 标记为自动关闭
          return true;
        }
        await wait(100);
      }

      // 5. 如果窗口未自动关闭，处理选择框
      if (!isAutoClose) {  // 只有在非自动关闭时才继续
        Statistics.addLog('⚠️ 窗口未自动关闭，直接再次点击保存');
  
        // 7. 再次检测窗口是否自动关闭
        const secondStartTime = Date.now();
        while (Date.now() - secondStartTime < maxWaitTime) {
          const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
          if (currentDialogs.length < initialCount) {
            Statistics.addLog('✅ 检测到窗口已自动关闭');
            Statistics.addSuccess(); // 自动关闭，记为成功
            isAutoClose = true;  // 标记为自动关闭
            return true;
          }
          await wait(100);
        }
  
        // 8. 如果还是未自动关闭，尝试手动关闭（这种情况算作失败）
        if (!isAutoClose) {  // 确保只在非自动关闭时执行
          Statistics.addLog('⚠️ 窗口仍未自动关闭，尝试手动关闭', 'warning');
          Statistics.addFailure(); // 在这里只记录一次失败
          
          const closeButton = document.querySelector("#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__header > button > i") || 
                             document.querySelector("#app > section > section > main > section > div > div:nth-child(6) > div > div.el-dialog__header > button > i");
          
          if (closeButton) {
            Statistics.addLog('🖱️ 点击关闭按钮');
            closeButton.click();
            await wait(200);
            
            const finalDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
            if (finalDialogs.length < initialCount) {
              Statistics.addLog('✅ 手动关闭窗口成功，将跳过此条记录');
              Statistics.currentIndex++;
              Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
              await wait(500);
              return true;
            }
          }

          Statistics.addLog('❌ 窗口关闭失败，将跳过当前记录', 'warning');
          Statistics.currentIndex++;
          Statistics.addLog(`📍 移动到第 ${Statistics.currentIndex + 1} 条记录`);
          await wait(500);
        }
      }
      return true;
    }
    await wait(300);
    attempts++;
  }
  throw new Error('未找到文本输入框');
}

// 添加检查元素是否在容器可视区域内的辅助函数
function isElementVisible(element, container) {
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  
  return (
    elementRect.top >= containerRect.top &&
    elementRect.bottom <= containerRect.bottom
  );
}

// 修改 performSingleFollow 函数
async function performSingleFollow() {
  try {
    // 首先检查是否需要跳过当前记录
    if (Statistics.skipCurrentRecord) {
      Statistics.skipCurrentRecord = false;
      Statistics.moveToNext(); // 移动到下一条记录
      Statistics.addLog('⏭️ 跳过当前记录，继续处理下一条');
      await wait(1000);
      return true;
    }

    // 查找跟进按钮
    const followButton = await findFollowButton();
    if (!followButton) {
      Statistics.addFailure(true);
      Statistics.addLog('❌ 未找到跟进按钮，等待10秒后重试', 'error');
      // 如果当前索引不为0，重置索引
      if (Statistics.currentIndex > 0) {
        Statistics.currentIndex = 0;
        Statistics.addLog('🔄 重置到第一条记录');
      }
      await wait(10000);
      return false;
    }
    
    Statistics.addLog('找到跟进按钮，准备点击');
    await clickButton(followButton);
    Statistics.addLog('已点击跟进按钮');
    
    await handleUpgradeDialog();
    
    const textInput = await findTextInput();
    Statistics.addLog('找到文本输入框');

    // 继续正常的处理流程
    const result = await inputText(textInput);
    
    if (result) {
      // 只有在没有设置skipCurrentRecord时才计算成功
      if (!Statistics.skipCurrentRecord) {
        Statistics.addSuccess();
        Statistics.addLog('本次跟进操作完成', 'success');
      }
      
      const waitTime = getRandomWaitTime();
      const waitTimeDisplay = waitTime < 1 ? 
        `${(waitTime * 1000).toFixed(0)}毫秒` : 
        `${waitTime}秒`;
      Statistics.addLog(`⏳ 等待${waitTimeDisplay}后开始下一轮...`);
      await wait(waitTime);
      return true;
    } else {
      Statistics.addFailure();
      Statistics.addLog('❌ 跟进操作失败', 'error');
      await wait(10000);
      return false;
    }
  } catch (error) {
    Statistics.addFailure();
    Statistics.addLog(`❌ ${error.message}`, 'error');
    await wait(10000);
    return false;
  }
}

// 添加查找下一条按钮的函数
async function findNextButton() {
  try {
    // 等待下一条按钮出现
    const nextButton = await waitForElement('button.el-button--default:not(.el-button--primary):not(.is-disabled)');
    if (nextButton && nextButton.textContent.includes('下一条')) {
      return nextButton;
    }
    return null;
  } catch (error) {
    Statistics.addLog('⚠️ 未找到下一条按钮', 'warning');
    return null;
  }
}

// 添加等待元素出现的辅助函数
async function waitForElement(selector, timeout = 5000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
    await wait(100);
  }
  return null;
}

// 优化开始自动化流程函数
async function startAutomation() {
  Statistics.start();
  Statistics.addLog('开始自动化流程');

  // 自动启动API分析
  Statistics.addLog('🔍 自动启动API分析模式...');
  if (window.networkAnalyzer) {
    window.networkAnalyzer.startAnalysis();
  }

  let consecutiveFailures = 0;
  const maxConsecutiveFailures = 3;
  const sleepTime = 30; // 30分钟
  
  while (isRunning) {
    try {
      Statistics.totalAttempts++;
      const success = await performSingleFollow();
      
      if (success) {
        consecutiveFailures = 0;
      } else {
        consecutiveFailures++;
        if (consecutiveFailures >= maxConsecutiveFailures) {
          // 进入休眠状态
          Statistics.setSleepMode(sleepTime);
          
          // 使用 while 循环代替 for 循环，以便更好地处理停止
          let sleepStartTime = Date.now();
          while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepTime * 60000) {
            await wait(1000); // 每秒检查一次
          }
          
          // 如果是因为停止而退出循环，确保清理状态
          if (!isRunning) {
            Statistics.clearSleepMode();
            break;
          }
          
          if (isRunning) {
            Statistics.clearSleepMode();
            Statistics.addLog('🔄 重新开始运行');
            consecutiveFailures = 0;
          }
        }
      }
    } catch (error) {
      consecutiveFailures++;
      if (consecutiveFailures >= maxConsecutiveFailures) {
        Statistics.setSleepMode(sleepTime);
        
        // 同样使用 while 循环处理休眠
        let sleepStartTime = Date.now();
        while (isRunning && Statistics.isSleeping && Date.now() - sleepStartTime < sleepTime * 60000) {
          await wait(1000);
        }
        
        // 如果是因为停止而退出循环，确保清理状态
        if (!isRunning) {
          Statistics.clearSleepMode();
          break;
        }
        
        if (isRunning) {
          Statistics.clearSleepMode();
          Statistics.addLog('🔄 重新开始运行');
          consecutiveFailures = 0;
        }
      } else {
        await wait(5000);
      }
    }
  }
  
  // 确保在退出时清理所有状态
  if (Statistics.isSleeping) {
    Statistics.clearSleepMode();
  }
  Statistics.addLog('自动化流程已停止');
}

// 修改 getRandomMessage 函数，添加默认消息和错误处理
function getRandomMessage() {
  // 默认消息列表
  const defaultMessages = [
    "您好，请问您最近有看车计划吗？",
    "您好，最近有考虑购车吗？",
    "请问您对哪款车型比较感兴趣呢？",
    "您好，需要了解具体车型的信息吗？",
    "最近店内有优惠活动，您有兴趣了解一下吗？"
  ];

  try {
    // 检查 settings 是否存在
    if (!settings || !settings.messages || !Array.isArray(settings.messages) || settings.messages.length === 0) {

      return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
    }

    // 过滤有效消息
    const validMessages = settings.messages.filter(msg => msg && typeof msg === 'string' && msg.trim());
    
    if (validMessages.length === 0) {
      Statistics.addLog('⚠️ 没有有效的自定义消息，使用默认消息');
      return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
    }

    return validMessages[Math.floor(Math.random() * validMessages.length)];
  } catch (error) {
    Statistics.addLog('⚠️ 获取消息出错，使用默认消息');
    return defaultMessages[Math.floor(Math.random() * defaultMessages.length)];
  }
}

// 修改获取随机等待时间的函数（用于跟进操作之间的等待）
function getRandomWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.1秒
  const safeMinTime = Math.max(0.1, minTime);
  const safeMaxTime = Math.max(safeMinTime, maxTime);

  // 生成随机等待时间（支持小数）
  const randomTime = Math.round((Math.random() * (safeMaxTime - safeMinTime) + safeMinTime) * 10) / 10;

  return randomTime;
}

// 新增：获取操作等待时间的函数（用于页面操作之间的等待）
function getOperationWaitTime() {
  // 将输入值转换为浮点数，支持小数
  const minTime = parseFloat(settings?.minWaitTime) || 0.1;
  const maxTime = parseFloat(settings?.maxWaitTime) || 0.2;

  // 确保最小值不小于0.05秒，最大值不超过0.5秒（页面操作应该更快）
  const safeMinTime = Math.max(0.05, Math.min(minTime, 0.3));
  const safeMaxTime = Math.max(safeMinTime, Math.min(maxTime, 0.5));

  // 生成随机等待时间（支持小数）
  const randomTime = Math.random() * (safeMaxTime - safeMinTime) + safeMinTime;
  return Math.round(randomTime * 100) / 100; // 保留2位小数
}

// 修改等待函数，支持小数秒
function wait(ms) {
  // 如果输入是秒，转换为毫秒
  if (ms < 100) { // 假设小于100的是秒单位
    ms = ms * 1000;
  }
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 极速智能检测：等待条件满足
async function fastWaitForCondition(conditionFn, maxAttempts = 50) {
  for (let i = 0; i < maxAttempts; i++) {
    if (conditionFn()) {
      return true;
    }
    await wait(1); // 1ms极速检测
  }
  return false;
}

// 极速智能检测：等待元素可见
async function fastWaitForVisible(selector, maxAttempts = 50) {
  return fastWaitForCondition(() => {
    const element = document.querySelector(selector);
    return element && element.offsetParent !== null;
  }, maxAttempts);
}

// 智能检测窗口是否关闭
async function smartDetectWindowClosed(initialCount, maxAttempts = 100) {
  return fastWaitForCondition(() => {
    // 方法1: 检查对话框数量
    const currentDialogs = document.querySelectorAll('.el-dialog__wrapper:not([style*="display: none"])');
    const visibleDialogs = Array.from(currentDialogs).filter(dialog => {
      const style = window.getComputedStyle(dialog);
      return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    });

    // 方法2: 检查对话框内容是否存在
    const dialogBodies = document.querySelectorAll('.el-dialog__body');
    const visibleBodies = Array.from(dialogBodies).filter(body => {
      const style = window.getComputedStyle(body);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    // 方法3: 检查遮罩层
    const masks = document.querySelectorAll('.el-dialog__wrapper .v-modal');
    const visibleMasks = Array.from(masks).filter(mask => {
      const style = window.getComputedStyle(mask);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });

    return visibleDialogs.length < initialCount ||
           visibleBodies.length === 0 ||
           visibleMasks.length === 0;
  }, maxAttempts);
}

// 消息监听
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'start') {
    if (!isRunning) {
      isRunning = true;
      settings = request.settings;

      // 简化设置日志
      if (settings.minWaitTime && settings.maxWaitTime) {
        Statistics.addLog(`⚙️ 等待时间设置: ${settings.minWaitTime}-${settings.maxWaitTime}秒`);
      }
      
      // 立即更新状态
      Statistics.updatePopup();
      
      // 发送立即状态更新
      chrome.runtime.sendMessage({
        action: 'updateSleepStatus',
        data: {
          isSleeping: Statistics.isSleeping,
          remainingTime: 0,
          sleepEndTime: null,
          isRunning: true
        }
      });

      startAutomation().catch(error => {
        console.error('自动化流程错误:', error);
        Statistics.addLog('运行错误: ' + error.message, 'error');
        isRunning = false;
        // 出错时更新状态
        Statistics.updatePopup();
      });
      
      sendResponse({ 
        status: 'started',
        currentState: {
          isRunning: true,
          isSleeping: Statistics.isSleeping,
          stats: Statistics.getStats()
        }
      });
    } else {
      sendResponse({ status: 'error', error: '已经在运行中' });
    }
    return true;
  }

  if (request.action === 'stop') {
    isRunning = false;

    // 停止API分析并导出结果
    if (window.networkAnalyzer && window.networkAnalyzer.isAnalyzing) {
      Statistics.addLog('🔍 停止API分析并导出结果...');
      window.networkAnalyzer.stopAnalysis();
    }

    // 立即清除休眠状态
    if (Statistics.isSleeping) {
      Statistics.clearSleepMode();
    }

    // 立即发送停止状态
    chrome.runtime.sendMessage({
      action: 'updateSleepStatus',
      data: {
        isSleeping: false,
        timeDisplay: '',
        remainingMs: 0,
        isRunning: false
      }
    });

    Statistics.addLog('停止自动化流程');
    Statistics.updatePopup();
    
    sendResponse({ status: 'stopped' });
    return true;
  }

  if (request.action === 'getStats') {
    sendResponse(Statistics.getStats());
  }

  if (request.action === 'getState') {
    sendResponse({
      isRunning,
      stats: Statistics.getStats()
    });
    return true;
  }

  if (request.action === 'resetStats') {
    Statistics.reset();
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'startNetworkAnalysis') {
    if (window.networkAnalyzer) {
      if (window.networkAnalyzer.isAnalyzing) {
        window.networkAnalyzer.stopAnalysis();
        Statistics.addLog('📊 网络分析已停止，请查看插件中的结果');
      } else {
        window.networkAnalyzer.startAnalysis();
        Statistics.addLog('🔍 网络分析已启动，请手动执行跟进操作');
      }
    }
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'downloadAnalysis') {
    if (window.analysisData && window.analysisDownloadUrl) {
      // 创建下载
      const link = document.createElement('a');
      link.href = window.analysisDownloadUrl;
      link.download = `API分析报告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      Statistics.addLog('📁 分析报告已下载');
    } else {
      Statistics.addLog('❌ 没有可下载的分析报告', 'error');
    }
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'startPostMode') {
    try {
      const customers = window.directAPISubmitter.extractCustomerData();
      Statistics.addLog(`🚀 POST模式: 检测到 ${customers.length} 条客户记录`);

      if (customers.length === 0) {
        Statistics.addLog('❌ 未检测到客户数据，请确保页面已加载客户列表', 'error');
        sendResponse({ success: false, error: '未检测到客户数据' });
        return true;
      }

      // 启动POST批量提交
      window.directAPISubmitter.batchSubmitFollowUp(customers, 2).then(result => {
        Statistics.addLog(`🎉 POST模式完成: 成功 ${result.successful}, 失败 ${result.failed}`);
        chrome.runtime.sendMessage({
          action: 'postModeComplete',
          data: result
        });
      }).catch(error => {
        Statistics.addLog(`❌ POST模式错误: ${error.message}`, 'error');
      });

      sendResponse({ success: true, customerCount: customers.length });
    } catch (error) {
      Statistics.addLog(`❌ POST模式启动失败: ${error.message}`, 'error');
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }

  if (request.action === 'testDataExtraction') {
    try {
      Statistics.addLog('🔍 开始测试数据提取...');
      const customers = window.directAPISubmitter.extractCustomerData();

      if (customers.length > 0) {
        Statistics.addLog(`✅ 数据提取成功: ${customers.length} 条记录`);
        sendResponse({ success: true, customers });
      } else {
        Statistics.addLog('❌ 未提取到客户数据', 'error');
        sendResponse({ success: false, error: '未提取到客户数据' });
      }
    } catch (error) {
      Statistics.addLog(`❌ 数据提取失败: ${error.message}`, 'error');
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }

  if (request.action === 'testPostSubmit') {
    try {
      const customers = window.directAPISubmitter.extractCustomerData();
      if (customers.length === 0) {
        Statistics.addLog('❌ 未检测到客户数据', 'error');
        sendResponse({ success: false, error: '未检测到客户数据' });
        return true;
      }

      Statistics.addLog('🧪 测试POST提交 - 只处理第一条记录');
      const testCustomer = customers[0];

      window.directAPISubmitter.submitSingleFollowUp(testCustomer).then(result => {
        Statistics.addLog(`🧪 测试完成: ${result.success ? '成功' : '失败'}`);
        sendResponse({ success: true, result });
      }).catch(error => {
        Statistics.addLog(`❌ 测试失败: ${error.message}`, 'error');
        sendResponse({ success: false, error: error.message });
      });

    } catch (error) {
      Statistics.addLog(`❌ 测试启动失败: ${error.message}`, 'error');
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }
});

// 网络请求分析器（用于POST方案研究）
class NetworkAnalyzer {
  constructor() {
    this.requests = [];
    this.allRequests = []; // 保存所有请求用于调试
    this.isAnalyzing = false;
    this.debugMode = false;
  }

  startAnalysis() {
    if (this.isAnalyzing) return;

    this.isAnalyzing = true;
    this.requests = [];
    this.allRequests = [];
    Statistics.addLog('🔍 开始网络请求分析模式...');

    this.setupInterceptors();
    this.setupFormListeners();
    this.setupButtonListeners();
    this.setupDOMObserver();
    this.setupGlobalListeners();
  }

  stopAnalysis() {
    this.isAnalyzing = false;

    // 停止DOM观察器
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    Statistics.addLog(`📊 网络分析完成，捕获 ${this.requests.length} 个相关请求`);
    this.exportAnalysis();
  }

  setupInterceptors() {
    const analyzer = this;

    // 更强力的fetch拦截
    if (window.fetch) {
      const originalFetch = window.fetch;
      window.fetch = async function(...args) {
        if (analyzer.isAnalyzing) {
          const url = args[0];
          const options = args[1] || {};
          const method = options.method || 'GET';
          const body = options.body;

          Statistics.addLog(`🔍 拦截Fetch: ${method} ${url}`);

          const requestData = {
            url: url,
            method: method,
            data: body,
            headers: options.headers,
            timestamp: new Date(),
            type: 'fetch'
          };

          analyzer.allRequests.push(requestData);
          analyzer.logRequest('fetch', requestData);
        }

        try {
          const response = await originalFetch.apply(this, args);

          if (analyzer.isAnalyzing) {
            Statistics.addLog(`📡 Fetch响应: ${response.status} ${args[0]}`);
          }

          return response;
        } catch (error) {
          if (analyzer.isAnalyzing) {
            Statistics.addLog(`❌ Fetch错误: ${error.message} ${args[0]}`);
          }
          throw error;
        }
      };
    }

    // 更强力的XMLHttpRequest拦截
    if (window.XMLHttpRequest) {
      const originalXHR = window.XMLHttpRequest;
      window.XMLHttpRequest = function() {
        const xhr = new originalXHR();

        if (analyzer.isAnalyzing) {
          const originalOpen = xhr.open;
          const originalSend = xhr.send;
          const originalSetRequestHeader = xhr.setRequestHeader;

          xhr._headers = {};

          xhr.open = function(method, url, async, user, password) {
            this._method = method;
            this._url = url;
            this._async = async;

            Statistics.addLog(`🔍 拦截XHR Open: ${method} ${url}`);

            return originalOpen.apply(this, arguments);
          };

          xhr.setRequestHeader = function(name, value) {
            this._headers[name] = value;
            return originalSetRequestHeader.apply(this, arguments);
          };

          xhr.send = function(data) {
            this._data = data;
            this._timestamp = new Date();

            Statistics.addLog(`🔍 拦截XHR Send: ${this._method} ${this._url}`);
            if (data) {
              Statistics.addLog(`📦 XHR数据: ${typeof data === 'string' ? data.substring(0, 200) : '[非字符串数据]'}`);
            }

            return originalSend.apply(this, arguments);
          };

          // 监听所有状态变化
          xhr.addEventListener('readystatechange', function() {
            if (analyzer.isAnalyzing) {
              Statistics.addLog(`🔄 XHR状态变化: ${this.readyState} ${this._url}`);
            }
          });

          xhr.addEventListener('load', function() {
            if (analyzer.isAnalyzing) {
              Statistics.addLog(`📡 XHR完成: ${this.status} ${this._url}`);

              const requestData = {
                method: this._method,
                url: this._url,
                data: this._data,
                headers: this._headers,
                status: this.status,
                response: this.responseText,
                timestamp: this._timestamp,
                type: 'xhr'
              };

              analyzer.allRequests.push(requestData);
              analyzer.logRequest('xhr', requestData);
            }
          });

          xhr.addEventListener('error', function() {
            if (analyzer.isAnalyzing) {
              Statistics.addLog(`❌ XHR错误: ${this._url}`);
            }
          });
        }

        return xhr;
      };
    }
  }

  setupFormListeners() {
    // 监听表单提交事件
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (event) => {
        if (this.isAnalyzing) {
          const formData = new FormData(form);
          const data = {};
          for (let [key, value] of formData.entries()) {
            data[key] = value;
          }

          Statistics.addLog(`📝 捕获表单提交: ${form.action || '当前页面'}`);
          this.logRequest('form', {
            url: form.action || window.location.href,
            method: form.method || 'POST',
            data: data,
            timestamp: new Date()
          });
        }
      });
    });
  }

  setupButtonListeners() {
    // 监听保存、提交等按钮点击
    const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
    buttons.forEach(button => {
      if (button.textContent.includes('保存') ||
          button.textContent.includes('提交') ||
          button.textContent.includes('确定') ||
          button.value?.includes('保存') ||
          button.value?.includes('提交')) {

        button.addEventListener('click', (event) => {
          if (this.isAnalyzing) {
            Statistics.addLog(`🖱️ 捕获按钮点击: ${button.textContent || button.value}`);

            // 尝试获取相关的表单数据
            const form = button.closest('form');
            if (form) {
              const formData = new FormData(form);
              const data = {};
              for (let [key, value] of formData.entries()) {
                data[key] = value;
              }

              this.logRequest('button', {
                url: form.action || window.location.href,
                method: 'POST',
                data: data,
                buttonText: button.textContent || button.value,
                timestamp: new Date()
              });
            }
          }
        });
      }
    });
  }

  setupDOMObserver() {
    // 观察DOM变化，捕获动态添加的表单和按钮
    this.observer = new MutationObserver((mutations) => {
      if (!this.isAnalyzing) return;

      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查新添加的表单
            if (node.tagName === 'FORM') {
              this.setupFormListener(node);
            }

            // 检查新添加的按钮
            if (node.tagName === 'BUTTON' ||
                (node.tagName === 'INPUT' && (node.type === 'submit' || node.type === 'button'))) {
              this.setupButtonListener(node);
            }

            // 检查子元素中的表单和按钮
            const forms = node.querySelectorAll?.('form');
            const buttons = node.querySelectorAll?.('button, input[type="submit"], input[type="button"]');

            forms?.forEach(form => this.setupFormListener(form));
            buttons?.forEach(button => this.setupButtonListener(button));
          }
        });
      });
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  setupFormListener(form) {
    form.addEventListener('submit', () => {
      if (this.isAnalyzing) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
          data[key] = value;
        }

        Statistics.addLog(`📝 捕获动态表单提交: ${form.action || '当前页面'}`);
        this.logRequest('form', {
          url: form.action || window.location.href,
          method: form.method || 'POST',
          data: data,
          timestamp: new Date()
        });
      }
    });
  }

  setupButtonListener(button) {
    if (button.textContent?.includes('保存') ||
        button.textContent?.includes('提交') ||
        button.textContent?.includes('确定') ||
        button.value?.includes('保存') ||
        button.value?.includes('提交')) {

      button.addEventListener('click', () => {
        if (this.isAnalyzing) {
          Statistics.addLog(`🖱️ 捕获动态按钮点击: ${button.textContent || button.value}`);

          const form = button.closest('form');
          if (form) {
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
              data[key] = value;
            }

            this.logRequest('button', {
              url: form.action || window.location.href,
              method: 'POST',
              data: data,
              buttonText: button.textContent || button.value,
              timestamp: new Date()
            });
          }
        }
      });
    }
  }

  setupGlobalListeners() {
    // 监听所有可能的网络活动
    const analyzer = this;

    // 监听页面的所有事件
    ['click', 'submit', 'change', 'input'].forEach(eventType => {
      document.addEventListener(eventType, function(event) {
        if (!analyzer.isAnalyzing) return;

        const target = event.target;
        const tagName = target.tagName?.toLowerCase();

        if (eventType === 'click') {
          if (tagName === 'button' || (tagName === 'input' && ['submit', 'button'].includes(target.type))) {
            const text = target.textContent || target.value || target.innerText;
            if (text && (text.includes('保存') || text.includes('提交') || text.includes('确定'))) {
              Statistics.addLog(`🎯 全局捕获关键按钮点击: ${text}`);

              // 延迟检查网络请求
              setTimeout(() => {
                analyzer.checkRecentNetworkActivity();
              }, 100);
            }
          }
        }

        if (eventType === 'submit' && tagName === 'form') {
          Statistics.addLog(`🎯 全局捕获表单提交`);
          setTimeout(() => {
            analyzer.checkRecentNetworkActivity();
          }, 100);
        }
      }, true); // 使用捕获阶段
    });

    // 监听页面卸载和加载事件
    window.addEventListener('beforeunload', () => {
      if (analyzer.isAnalyzing) {
        Statistics.addLog(`🔄 页面即将卸载`);
      }
    });

    window.addEventListener('load', () => {
      if (analyzer.isAnalyzing) {
        Statistics.addLog(`🔄 页面加载完成`);
      }
    });

    // 监听hash变化（SPA应用）
    window.addEventListener('hashchange', () => {
      if (analyzer.isAnalyzing) {
        Statistics.addLog(`🔄 Hash变化: ${window.location.hash}`);
      }
    });

    // 监听history变化（SPA应用）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      if (analyzer.isAnalyzing) {
        Statistics.addLog(`🔄 History pushState: ${args[2]}`);
      }
      return originalPushState.apply(this, args);
    };

    history.replaceState = function(...args) {
      if (analyzer.isAnalyzing) {
        Statistics.addLog(`🔄 History replaceState: ${args[2]}`);
      }
      return originalReplaceState.apply(this, args);
    };
  }

  checkRecentNetworkActivity() {
    // 检查最近是否有网络活动
    const recentRequests = this.allRequests.filter(req => {
      const timeDiff = Date.now() - new Date(req.timestamp).getTime();
      return timeDiff < 5000; // 5秒内的请求
    });

    if (recentRequests.length > 0) {
      Statistics.addLog(`📡 检测到 ${recentRequests.length} 个最近的网络请求`);
      recentRequests.forEach(req => {
        Statistics.addLog(`  - ${req.method || 'GET'} ${req.url}`);
      });
    } else {
      Statistics.addLog(`⚠️ 未检测到最近的网络请求`);
      this.tryAlternativeDetection();
    }
  }

  tryAlternativeDetection() {
    // 尝试其他检测方法
    Statistics.addLog(`🔍 尝试替代检测方法...`);

    // 检查页面中是否有Vue、React、Angular等框架
    const frameworks = [];
    if (window.Vue) frameworks.push('Vue');
    if (window.React) frameworks.push('React');
    if (window.angular) frameworks.push('Angular');
    if (window.jQuery || window.$) frameworks.push('jQuery');

    if (frameworks.length > 0) {
      Statistics.addLog(`🎯 检测到前端框架: ${frameworks.join(', ')}`);
    }

    // 检查是否有axios、jQuery等网络库
    const networkLibs = [];
    if (window.axios) networkLibs.push('axios');
    if (window.jQuery?.ajax) networkLibs.push('jQuery.ajax');
    if (window.$?.ajax) networkLibs.push('$.ajax');

    if (networkLibs.length > 0) {
      Statistics.addLog(`🌐 检测到网络库: ${networkLibs.join(', ')}`);
      this.interceptNetworkLibraries();
    }

    // 检查全局变量中是否有API相关的对象
    const globalVars = Object.keys(window).filter(key =>
      key.toLowerCase().includes('api') ||
      key.toLowerCase().includes('request') ||
      key.toLowerCase().includes('http') ||
      key.toLowerCase().includes('ajax')
    );

    if (globalVars.length > 0) {
      Statistics.addLog(`🔍 发现可能的API相关全局变量: ${globalVars.join(', ')}`);
    }
  }

  interceptNetworkLibraries() {
    // 拦截常见的网络库
    const analyzer = this;

    // 拦截axios
    if (window.axios) {
      const originalAxios = window.axios;
      window.axios = function(...args) {
        if (analyzer.isAnalyzing) {
          Statistics.addLog(`🎯 拦截Axios请求: ${JSON.stringify(args[0])}`);
        }
        return originalAxios.apply(this, args);
      };

      // 保持axios的属性
      Object.setPrototypeOf(window.axios, originalAxios);
      Object.keys(originalAxios).forEach(key => {
        window.axios[key] = originalAxios[key];
      });
    }

    // 拦截jQuery.ajax
    if (window.jQuery?.ajax) {
      const originalAjax = window.jQuery.ajax;
      window.jQuery.ajax = function(options) {
        if (analyzer.isAnalyzing) {
          Statistics.addLog(`🎯 拦截jQuery.ajax: ${options.url || options}`);
        }
        return originalAjax.apply(this, arguments);
      };
    }

    if (window.$?.ajax && window.$ !== window.jQuery) {
      const originalAjax = window.$.ajax;
      window.$.ajax = function(options) {
        if (analyzer.isAnalyzing) {
          Statistics.addLog(`🎯 拦截$.ajax: ${options.url || options}`);
        }
        return originalAjax.apply(this, arguments);
      };
    }
  }

  logRequest(type, data) {
    // 记录所有请求用于调试
    this.allRequests.push({ type, ...data });

    // 扩展过滤条件，捕获更多可能的请求
    const url = data.url || '';
    const method = data.method || 'GET';

    // 排除明显无关的请求
    const isStaticResource = url.match(/\.(js|css|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)(\?|$)/i);
    const isGoogleAnalytics = url.includes('google-analytics') || url.includes('gtag') || url.includes('googletagmanager');
    const isChromeExtension = url.startsWith('chrome-extension://');
    const isWebpackHMR = url.includes('webpack') || url.includes('hot-update');

    if (isStaticResource || isGoogleAnalytics || isChromeExtension || isWebpackHMR) {
      return; // 跳过这些请求
    }

    // 更宽松的过滤条件，捕获几乎所有业务请求
    const isRelevant =
      // 包含关键词的请求
      url.includes('follow') ||
      url.includes('save') ||
      url.includes('update') ||
      url.includes('submit') ||
      url.includes('api') ||
      url.includes('customer') ||
      url.includes('crm') ||
      url.includes('lead') ||
      url.includes('contact') ||
      url.includes('record') ||
      url.includes('form') ||
      url.includes('data') ||
      // POST/PUT/PATCH 方法的请求
      method === 'POST' ||
      method === 'PUT' ||
      method === 'PATCH' ||
      // 包含数据的请求
      (data.data && data.data !== '' && data.data !== null) ||
      // 同域名的非静态请求
      (url.startsWith(window.location.origin) &&
       !url.includes('/static/') &&
       !url.includes('/assets/') &&
       !url.includes('/public/')) ||
      // 状态码表明是业务请求
      (data.status && (data.status >= 200 && data.status < 300)) ||
      // 包含audiep域名的请求（基于抓包数据）
      url.includes('audiep.faw-vw.com');

    if (isRelevant) {
      this.requests.push({ type, ...data });
      Statistics.addLog(`🌐 捕获请求: ${method} ${url.substring(0, 80)}${url.length > 80 ? '...' : ''}`);

      // 如果是POST请求，特别标注
      if (method === 'POST') {
        Statistics.addLog(`🎯 重要: 发现POST请求 - ${url}`);
      }
    }
  }

  exportAnalysis() {
    const analysis = {
      totalRequests: this.requests.length,
      totalAllRequests: this.allRequests.length,
      requests: this.requests,
      allRequests: this.allRequests, // 包含所有请求用于调试
      summary: this.generateSummary(),
      timestamp: new Date().toISOString(),
      analysisId: Date.now()
    };

    console.log('=== 🔍 网络请求分析结果 ===');
    console.log('📋 相关请求数:', analysis.totalRequests);
    console.log('📋 总请求数:', analysis.totalAllRequests);
    console.log('📊 相关请求:', analysis.requests);
    console.log('🔧 所有请求(调试):', analysis.allRequests);
    console.log('📊 完整数据:', JSON.stringify(analysis, null, 2));

    // 保存到localStorage
    localStorage.setItem('networkAnalysis', JSON.stringify(analysis));

    // 发送分析结果到popup
    chrome.runtime.sendMessage({
      action: 'analysisComplete',
      data: analysis
    });

    // 生成下载链接
    this.createDownloadLink(analysis);

    Statistics.addLog('📋 分析完成！结果已显示在插件中');
    Statistics.addLog(`📊 捕获 ${analysis.totalRequests} 个相关请求，总共 ${analysis.totalAllRequests} 个请求`);
    Statistics.addLog('💾 可点击插件中的"下载报告"按钮保存');

    if (analysis.totalRequests === 0 && analysis.totalAllRequests > 0) {
      Statistics.addLog('💡 提示：检测到网络请求但未匹配过滤条件，请查看控制台的"所有请求"');
    }
  }

  createDownloadLink(analysis) {
    // 创建下载链接
    const blob = new Blob([JSON.stringify(analysis, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);

    // 保存下载URL到全局变量
    window.analysisDownloadUrl = url;
    window.analysisData = analysis;

    Statistics.addLog('📁 分析报告已准备就绪，可下载');
  }

  generateSummary() {
    const postRequests = this.requests.filter(r => r.method === 'POST');
    const putRequests = this.requests.filter(r => r.method === 'PUT');
    const apiEndpoints = [...new Set(this.requests.map(r => r.url))];

    return {
      postRequests: postRequests.length,
      putRequests: putRequests.length,
      totalEndpoints: apiEndpoints.length,
      endpoints: apiEndpoints,
      sampleData: this.extractSampleData()
    };
  }

  extractSampleData() {
    const samples = {};

    this.requests.forEach(req => {
      if (req.data) {
        try {
          const data = typeof req.data === 'string' ? JSON.parse(req.data) : req.data;
          const endpoint = req.url.split('?')[0]; // 移除查询参数

          if (!samples[endpoint]) {
            samples[endpoint] = {
              method: req.method,
              samplePayload: data,
              count: 1
            };
          } else {
            samples[endpoint].count++;
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    });

    return samples;
  }
}

// 全局网络分析器实例
window.networkAnalyzer = new NetworkAnalyzer();

// 简化的网络调试器
class SimpleNetworkDebugger {
  constructor() {
    this.requests = [];
    this.isActive = false;
  }

  start() {
    if (this.isActive) return;
    this.isActive = true;
    this.requests = [];

    console.log('🔧 简化网络调试器启动');
    this.interceptBasic();
  }

  stop() {
    this.isActive = false;
    console.log(`🔧 调试器停止，捕获 ${this.requests.length} 个请求`);

    if (this.requests.length > 0) {
      console.log('📋 捕获的请求:');
      this.requests.forEach((req, i) => {
        console.log(`${i+1}. ${req.method} ${req.url}`);
        if (req.data) console.log(`   数据:`, req.data);
      });
    } else {
      console.log('❌ 未捕获到任何请求');
      console.log('💡 建议检查浏览器Network面板');
    }
  }

  interceptBasic() {
    const self = this;

    // 最基础的拦截
    if (window.fetch) {
      const originalFetch = window.fetch;
      window.fetch = function(...args) {
        if (self.isActive) {
          console.log(`🌐 DEBUG FETCH: ${args[1]?.method || 'GET'} ${args[0]}`);
          self.requests.push({
            type: 'fetch',
            method: args[1]?.method || 'GET',
            url: args[0],
            data: args[1]?.body,
            timestamp: new Date()
          });
        }
        return originalFetch.apply(this, args);
      };
    }

    if (window.XMLHttpRequest) {
      const OriginalXHR = window.XMLHttpRequest;
      window.XMLHttpRequest = function() {
        const xhr = new OriginalXHR();

        if (self.isActive) {
          const originalSend = xhr.send;
          xhr.send = function(data) {
            console.log(`🌐 DEBUG XHR: ${this._method || 'GET'} ${this._url || 'unknown'}`);
            self.requests.push({
              type: 'xhr',
              method: this._method || 'GET',
              url: this._url || 'unknown',
              data: data,
              timestamp: new Date()
            });
            return originalSend.apply(this, arguments);
          };
        }

        return xhr;
      };
    }
  }
}

window.simpleDebugger = new SimpleNetworkDebugger();

// 调试命令
window.startNetworkDebug = () => {
  window.simpleDebugger.start();
  console.log('🚀 请执行跟进操作，然后运行 stopNetworkDebug()');
};

window.stopNetworkDebug = () => {
  window.simpleDebugger.stop();
};

// 测试数据提取
window.testDataExtraction = () => {
  console.log('🔍 测试客户数据提取...');
  const customers = window.directAPISubmitter.extractCustomerData();

  console.log(`📊 提取结果: ${customers.length} 条客户数据`);
  customers.forEach((customer, index) => {
    console.log(`${index + 1}. ID: ${customer.leadId}, 姓名: ${customer.userName}, 手机: ${customer.userMobile}`);
  });

  if (customers.length === 0) {
    console.log('❌ 未提取到客户数据');
    console.log('💡 建议检查页面是否已加载客户列表');
  } else {
    console.log('✅ 数据提取成功，可以使用POST模式');
  }

  return customers;
};

// 深度API监控器 - 更激进的拦截方案
class DeepAPIMonitor {
  constructor() {
    this.capturedRequests = [];
    this.isMonitoring = false;
    this.originalMethods = {};
  }

  startDeepMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.capturedRequests = [];
    Statistics.addLog('🔬 启动深度API监控...');

    this.interceptAllNetworkMethods();
    this.interceptJQueryAjax();
    this.interceptAxios();
    this.monitorGlobalVariables();
    this.setupPerformanceObserver();
  }

  stopDeepMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.restoreOriginalMethods();

    Statistics.addLog(`🔬 深度监控完成，捕获 ${this.capturedRequests.length} 个请求`);
    this.exportCapturedRequests();
  }

  interceptAllNetworkMethods() {
    // 拦截所有可能的网络方法
    const methods = ['fetch', 'XMLHttpRequest'];

    // 保存原始方法
    this.originalMethods.fetch = window.fetch;
    this.originalMethods.XMLHttpRequest = window.XMLHttpRequest;

    // 拦截fetch
    window.fetch = (...args) => {
      this.logDeepRequest('fetch', args);
      return this.originalMethods.fetch.apply(window, args);
    };

    // 拦截XMLHttpRequest
    window.XMLHttpRequest = function() {
      const xhr = new window.DeepAPIMonitor.originalMethods.XMLHttpRequest();
      const monitor = window.deepAPIMonitor;

      // 拦截open方法
      const originalOpen = xhr.open;
      xhr.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        monitor.logDeepRequest('xhr-open', { method, url });
        return originalOpen.apply(this, [method, url, ...args]);
      };

      // 拦截send方法
      const originalSend = xhr.send;
      xhr.send = function(data) {
        this._data = data;
        monitor.logDeepRequest('xhr-send', {
          method: this._method,
          url: this._url,
          data: data
        });
        return originalSend.apply(this, [data]);
      };

      return xhr;
    };

    // 保持原始构造函数的引用
    window.DeepAPIMonitor = { originalMethods: this.originalMethods };
  }

  interceptJQueryAjax() {
    // 拦截jQuery AJAX（如果存在）
    if (window.$ && window.$.ajax) {
      this.originalMethods.jqueryAjax = window.$.ajax;

      window.$.ajax = (options) => {
        this.logDeepRequest('jquery-ajax', options);
        return this.originalMethods.jqueryAjax.call(window.$, options);
      };
    }
  }

  interceptAxios() {
    // 拦截Axios（如果存在）
    if (window.axios) {
      this.originalMethods.axios = window.axios;

      const monitor = this;
      window.axios = new Proxy(window.axios, {
        get(target, prop) {
          if (typeof target[prop] === 'function') {
            return function(...args) {
              monitor.logDeepRequest('axios-' + prop, args);
              return target[prop].apply(target, args);
            };
          }
          return target[prop];
        }
      });
    }
  }

  monitorGlobalVariables() {
    // 监控可能的全局API调用函数
    const possibleApiMethods = [
      'submitForm', 'saveData', 'updateRecord', 'followUp',
      'postData', 'sendRequest', 'apiCall', 'request'
    ];

    possibleApiMethods.forEach(methodName => {
      if (window[methodName] && typeof window[methodName] === 'function') {
        this.originalMethods[methodName] = window[methodName];

        window[methodName] = (...args) => {
          this.logDeepRequest('global-' + methodName, args);
          return this.originalMethods[methodName].apply(window, args);
        };
      }
    });
  }

  setupPerformanceObserver() {
    // 使用Performance API监控网络请求
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        if (!this.isMonitoring) return;

        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'navigation' || entry.entryType === 'resource') {
            this.logDeepRequest('performance', {
              name: entry.name,
              type: entry.entryType,
              duration: entry.duration,
              transferSize: entry.transferSize
            });
          }
        });
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'resource'] });
    }
  }

  logDeepRequest(type, data) {
    if (!this.isMonitoring) return;

    const request = {
      type,
      data,
      timestamp: new Date(),
      stack: new Error().stack
    };

    this.capturedRequests.push(request);
    Statistics.addLog(`🔬 深度捕获: ${type} - ${JSON.stringify(data).substring(0, 100)}`);
  }

  restoreOriginalMethods() {
    // 恢复所有原始方法
    Object.keys(this.originalMethods).forEach(key => {
      if (key === 'fetch') {
        window.fetch = this.originalMethods.fetch;
      } else if (key === 'XMLHttpRequest') {
        window.XMLHttpRequest = this.originalMethods.XMLHttpRequest;
      } else if (key === 'jqueryAjax' && window.$) {
        window.$.ajax = this.originalMethods.jqueryAjax;
      } else if (key === 'axios') {
        window.axios = this.originalMethods.axios;
      } else if (window[key]) {
        window[key] = this.originalMethods[key];
      }
    });

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }

  exportCapturedRequests() {
    const analysis = {
      totalRequests: this.capturedRequests.length,
      requests: this.capturedRequests,
      timestamp: new Date().toISOString(),
      summary: this.generateSummary()
    };

    console.log('=== 🔬 深度API监控结果 ===');
    console.log(JSON.stringify(analysis, null, 2));

    localStorage.setItem('deepAPIAnalysis', JSON.stringify(analysis));

    // 发送到popup
    chrome.runtime.sendMessage({
      action: 'deepAnalysisComplete',
      data: analysis
    });
  }

  generateSummary() {
    const types = {};
    this.capturedRequests.forEach(req => {
      types[req.type] = (types[req.type] || 0) + 1;
    });

    return {
      requestTypes: types,
      totalCount: this.capturedRequests.length
    };
  }
}

// 全局深度监控器实例
window.deepAPIMonitor = new DeepAPIMonitor();

// 基于真实抓包数据的POST直接提交类
class DirectAPISubmitter {
  constructor() {
    this.baseURL = 'https://audiep.faw-vw.com/api/adc/v1/lead';
    this.headers = this.getAuthHeaders();
  }

  getAuthHeaders() {
    // 获取当前页面的认证信息
    const cookies = document.cookie;
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      'Referer': window.location.href,
      'Origin': window.location.origin
    };
  }

  // 从页面提取客户数据（增强版）
  extractCustomerData() {
    const customers = [];

    try {
      Statistics.addLog('🔍 开始提取客户数据...');

      // 方法1: 从表格行提取
      const tableSelectors = [
        'tr[data-id]',
        '.el-table__row',
        'tbody tr',
        '.table-row',
        '[data-lead-id]'
      ];

      let rows = [];
      for (const selector of tableSelectors) {
        rows = document.querySelectorAll(selector);
        if (rows.length > 0) {
          Statistics.addLog(`📋 使用选择器 "${selector}" 找到 ${rows.length} 行数据`);
          break;
        }
      }

      if (rows.length === 0) {
        Statistics.addLog('⚠️ 未找到表格行，尝试其他方法...');
      }

      rows.forEach((row, index) => {
        const leadId = this.extractLeadId(row);
        const userName = this.extractUserName(row);
        const userMobile = this.extractUserMobile(row);

        if (leadId) {
          customers.push({
            leadId,
            userName: userName || `客户${index + 1}`,
            userMobile: userMobile || '',
            index: index + 1,
            element: row
          });
          Statistics.addLog(`✅ 提取客户: ID=${leadId}, 姓名=${userName || '未知'}`);
        }
      });

      // 方法2: 从JavaScript变量提取
      const jsVarNames = ['leadData', 'customerList', 'tableData', 'listData', 'data'];
      for (const varName of jsVarNames) {
        if (window[varName] && Array.isArray(window[varName])) {
          Statistics.addLog(`📊 从变量 ${varName} 提取数据`);
          window[varName].forEach((item, index) => {
            if (item.leadId || item.id) {
              customers.push({
                leadId: item.leadId || item.id,
                userName: item.userName || item.name || `客户${index + 1}`,
                userMobile: item.userMobile || item.mobile || item.phone || '',
                index: index + 1,
                source: varName
              });
            }
          });
          break;
        }
      }

      // 方法3: 从当前跟进按钮的上下文提取
      const followButtons = document.querySelectorAll('button');
      const followButtonsArray = Array.from(followButtons).filter(btn =>
        btn.textContent.includes('跟进') && !btn.disabled
      );

      if (followButtonsArray.length > 0 && customers.length === 0) {
        Statistics.addLog(`🔍 从 ${followButtonsArray.length} 个跟进按钮提取数据`);
        followButtonsArray.forEach((btn, index) => {
          const row = btn.closest('tr') || btn.closest('.el-table__row') || btn.closest('[data-id]');
          if (row) {
            const leadId = this.extractLeadId(row) || `auto_${Date.now()}_${index}`;
            const userName = this.extractUserName(row) || `客户${index + 1}`;
            const userMobile = this.extractUserMobile(row) || '';

            customers.push({
              leadId,
              userName,
              userMobile,
              index: index + 1,
              button: btn,
              element: row
            });
          }
        });
      }

    } catch (error) {
      Statistics.addLog(`❌ 提取客户数据失败: ${error.message}`, 'error');
    }

    Statistics.addLog(`📊 总共提取到 ${customers.length} 条客户数据`);
    return customers;
  }

  extractLeadId(element) {
    // 尝试多种方式提取leadId
    const methods = [
      () => element.dataset.id,
      () => element.dataset.leadId,
      () => element.dataset.key,
      () => element.getAttribute('data-id'),
      () => element.getAttribute('data-lead-id'),
      () => element.querySelector('[data-id]')?.dataset.id,
      () => element.querySelector('[data-lead-id]')?.dataset.leadId,
      () => element.querySelector('.lead-id')?.textContent?.trim(),
      () => element.querySelector('.id')?.textContent?.trim(),
      () => {
        // 从所有td中查找数字ID
        const tds = element.querySelectorAll('td');
        for (const td of tds) {
          const text = td.textContent?.trim();
          if (text && /^\d{6,}$/.test(text)) {
            return text;
          }
        }
      }
    ];

    for (const method of methods) {
      try {
        const result = method();
        if (result) return result;
      } catch (e) {
        // 忽略错误，继续下一个方法
      }
    }

    return null;
  }

  extractUserName(element) {
    // 尝试多种方式提取用户名
    const nameSelectors = [
      '.user-name', '.customer-name', '.name', '.client-name',
      'td:nth-child(2)', 'td:nth-child(3)', 'td:nth-child(4)',
      '[data-name]', '.customer', '.client'
    ];

    for (const selector of nameSelectors) {
      try {
        const nameEl = element.querySelector(selector);
        if (nameEl && nameEl.textContent.trim()) {
          const name = nameEl.textContent.trim();
          // 过滤掉明显不是姓名的内容
          if (name.length >= 2 && name.length <= 10 && !/^\d+$/.test(name)) {
            return name;
          }
        }
      } catch (e) {
        // 忽略错误
      }
    }

    // 如果没找到，尝试从所有td中查找可能的姓名
    const tds = element.querySelectorAll('td');
    for (const td of tds) {
      const text = td.textContent?.trim();
      if (text && text.length >= 2 && text.length <= 10 &&
          !/^\d+$/.test(text) && !/^1[3-9]\d{9}$/.test(text) &&
          !text.includes('跟进') && !text.includes('按钮')) {
        return text;
      }
    }

    return '';
  }

  extractUserMobile(element) {
    // 尝试多种方式提取手机号
    const mobileSelectors = [
      '.mobile', '.phone', '.user-mobile', '.tel', '.telephone',
      'td:nth-child(4)', 'td:nth-child(5)', 'td:nth-child(6)',
      '[data-mobile]', '[data-phone]'
    ];

    for (const selector of mobileSelectors) {
      try {
        const mobileEl = element.querySelector(selector);
        if (mobileEl && mobileEl.textContent.trim()) {
          const mobile = mobileEl.textContent.trim();
          // 验证手机号格式
          if (/^1[3-9]\d{9}$/.test(mobile)) {
            return mobile;
          }
        }
      } catch (e) {
        // 忽略错误
      }
    }

    // 从所有td中查找手机号
    const tds = element.querySelectorAll('td');
    for (const td of tds) {
      const text = td.textContent?.trim();
      if (text && /^1[3-9]\d{9}$/.test(text)) {
        return text;
      }
    }

    return '';
  }

  // 生成跟进数据
  generateFollowUpData(customer) {
    const now = new Date();
    const buyCarDate = new Date(now.getTime() + (Math.random() * 30 + 7) * 24 * 60 * 60 * 1000); // 7-37天后
    const nextFollowTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后

    return {
      leadId: customer.leadId,
      userName: customer.userName,
      userMobile: customer.userMobile,
      remark: this.getRandomMessage(),
      buyCarDate: this.formatDateTime(buyCarDate),
      nextFollowTime: this.formatDateTime(nextFollowTime),
      // 基于抓包数据的其他必需字段
      actualBuyer: "",
      actualBuyerPhone: "",
      bigState: "有效",
      bigStateId: 0,
      competition: "",
      consultant: "胥艳红", // 可以从页面动态获取
      consultantId: "117089", // 可以从页面动态获取
      currentCarFocus: "",
      customerFocus: "",
      failReason: "",
      followMethod: "电话沟通",
      intentionModel: "",
      intentionModelId: "",
      intentionSeries: "1011_A6L Limousine",
      intentionSeriesId: "1011",
      intentionSeriesName: "A6L Limousine",
      invalidReason: "",
      isFinancial: "",
      isInvalid: 0,
      isSleep: "",
      isTestDrive: "",
      level: "2_B（30天内跟进）_720",
      levelId: "2",
      levelName: "B（30天内跟进）",
      nextState: 201,
      policyFocus: "",
      purchaseBudget: "",
      purchaseType: "",
      recomender: "",
      recomenderChassisNo: "",
      recomenderLicensePlate: "",
      recomenderPhone: "",
      recommenderRegisteredTime: "",
      replacementType: 1,
      state: 201,
      stateName: "再次待跟进",
      vehicleChassisNo: "",
      vehicleConfig: ""
    };
  }

  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }

  getRandomMessage() {
    const messages = [
      "您好，请问您最近有看车计划吗？",
      "您好，最近有考虑购车吗？",
      "请问您对哪款车型比较感兴趣呢？",
      "您好，需要了解具体车型的信息吗？",
      "最近店内有优惠活动，您有兴趣了解一下吗？"
    ];

    if (settings && settings.messages && settings.messages.length > 0) {
      const validMessages = settings.messages.filter(msg => msg && msg.trim());
      if (validMessages.length > 0) {
        return validMessages[Math.floor(Math.random() * validMessages.length)];
      }
    }

    return messages[Math.floor(Math.random() * messages.length)];
  }

  // 提交单个跟进
  async submitSingleFollowUp(customer) {
    try {
      const followUpData = this.generateFollowUpData(customer);
      const timestamp = Date.now();

      const response = await fetch(`${this.baseURL}/followUp?_t=${timestamp}`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(followUpData),
        credentials: 'include' // 包含cookies
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.code === "000000") {
        Statistics.addLog(`✅ ${customer.userName}(${customer.leadId}) 跟进成功`);
        return { success: true, customer, result };
      } else {
        throw new Error(`API错误: ${result.description || result.code}`);
      }

    } catch (error) {
      Statistics.addLog(`❌ ${customer.userName}(${customer.leadId}) 跟进失败: ${error.message}`, 'error');
      return { success: false, customer, error: error.message };
    }
  }

  // 批量提交跟进
  async batchSubmitFollowUp(customers, concurrency = 2) {
    Statistics.addLog(`🚀 开始POST批量提交: ${customers.length} 条记录，${concurrency} 并发`);

    const results = [];
    let successful = 0;
    let failed = 0;

    // 分批处理
    for (let i = 0; i < customers.length; i += concurrency) {
      if (!isRunning) break;

      const batch = customers.slice(i, i + concurrency);
      Statistics.addLog(`📦 处理批次 ${Math.floor(i / concurrency) + 1}: 记录 ${i + 1}-${Math.min(i + concurrency, customers.length)}`);

      // 并发处理当前批次
      const promises = batch.map(customer => this.submitSingleFollowUp(customer));
      const batchResults = await Promise.allSettled(promises);

      // 统计结果
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successful++;
        } else {
          failed++;
        }
        results.push(result.value || { success: false, customer: batch[index], error: 'Promise rejected' });
      });

      Statistics.addLog(`📊 批次完成: 成功 ${batchResults.filter(r => r.status === 'fulfilled' && r.value.success).length}, 失败 ${batchResults.length - batchResults.filter(r => r.status === 'fulfilled' && r.value.success).length}`);

      // 批次间等待
      if (i + concurrency < customers.length) {
        await wait(1000 + Math.random() * 2000); // 1-3秒随机等待
      }
    }

    Statistics.addLog(`🎉 POST批量提交完成: 成功 ${successful}, 失败 ${failed}`);
    return { total: customers.length, successful, failed, results };
  }
}

// 全局POST提交器实例
window.directAPISubmitter = new DirectAPISubmitter();

// 添加控制台命令
window.startNetworkAnalysis = () => {
  window.networkAnalyzer.startAnalysis();
  console.log('🔍 网络分析已启动，请执行跟进操作...');
};

window.stopNetworkAnalysis = () => {
  window.networkAnalyzer.stopAnalysis();
  console.log('📊 网络分析已停止，请查看结果');
};

// POST模式控制台命令
window.startPostMode = async () => {
  try {
    const customers = window.directAPISubmitter.extractCustomerData();
    console.log(`🚀 POST模式: 检测到 ${customers.length} 条客户记录`);

    if (customers.length === 0) {
      console.log('❌ 未检测到客户数据，请确保页面已加载客户列表');
      return;
    }

    console.log('📋 客户列表:', customers);

    const confirm = window.confirm(`🚀 POST直接提交模式\n\n检测到 ${customers.length} 条客户记录\n\n确定要开始批量提交吗？\n\n注意：这将直接调用API，速度很快！`);

    if (confirm) {
      const result = await window.directAPISubmitter.batchSubmitFollowUp(customers);
      console.log('🎉 POST模式完成:', result);
    }
  } catch (error) {
    console.error('❌ POST模式错误:', error);
  }
};

window.testPostSubmit = async () => {
  try {
    const customers = window.directAPISubmitter.extractCustomerData();
    if (customers.length === 0) {
      console.log('❌ 未检测到客户数据');
      return;
    }

    console.log('🧪 测试POST提交 - 只处理第一条记录');
    const testCustomer = customers[0];
    console.log('测试客户:', testCustomer);

    const result = await window.directAPISubmitter.submitSingleFollowUp(testCustomer);
    console.log('测试结果:', result);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
};

window.enableDebugMode = () => {
  window.networkAnalyzer.debugMode = true;
  console.log('🔧 调试模式已启用，将显示所有网络请求');
};

window.showAllRequests = () => {
  if (window.networkAnalyzer.allRequests.length > 0) {
    console.log('=== 🔧 所有拦截的请求 ===');
    window.networkAnalyzer.allRequests.forEach((req, index) => {
      console.log(`${index + 1}. [${req.type}] ${req.method || 'GET'} ${req.url}`);
      if (req.data) {
        console.log('   数据:', req.data);
      }
    });
  } else {
    console.log('❌ 没有拦截到任何请求');
  }
};

window.analyzeRequests = () => {
  const all = window.networkAnalyzer.allRequests;
  const relevant = window.networkAnalyzer.requests;

  console.log('=== 📊 请求分析 ===');
  console.log(`总请求数: ${all.length}`);
  console.log(`相关请求数: ${relevant.length}`);

  if (all.length > 0) {
    const methods = {};
    const domains = {};

    all.forEach(req => {
      const method = req.method || 'GET';
      methods[method] = (methods[method] || 0) + 1;

      try {
        const domain = new URL(req.url).hostname;
        domains[domain] = (domains[domain] || 0) + 1;
      } catch (e) {
        // 忽略无效URL
      }
    });

    console.log('请求方法分布:', methods);
    console.log('域名分布:', domains);
  }
};

// 发送就绪消息
console.log('[自动跟进助手] Content script准备就绪');
console.log('🚀 POST直接提交功能已加载！');
console.log('🔧 增强网络分析功能已加载！');
console.log('');
console.log('📝 推荐使用方法：');
console.log('   1. 先测试数据提取: testDataExtraction()');
console.log('   2. 点击插件的"🧪 测试POST" - 测试单条记录');
console.log('   3. 点击插件的"🚀 POST模式" - 批量处理所有记录');
console.log('');
console.log('🔧 调试命令：');
console.log('   testDataExtraction() - 测试客户数据提取');
console.log('   testPostSubmit() - 测试POST提交');
console.log('   startPostMode() - 启动POST批量模式');
console.log('   startNetworkDebug() - 网络调试（如需要）');
console.log('');
console.log('⚡ 基于真实抓包数据，预期效率提升10-60倍！');
console.log('💡 建议先运行 testDataExtraction() 检查数据提取是否正常');

chrome.runtime.sendMessage({
  action: 'contentScriptReady',
  url: window.location.href
});

// 辅助函数：获取元素的路径
function getElementPath(element) {
  const path = [];
  while (element && element.nodeType === Node.ELEMENT_NODE) {
    let selector = element.nodeName.toLowerCase();
    if (element.id) {
      selector += `#${element.id}`;
    } else if (element.className) {
      selector += `.${element.className.replace(/\s+/g, '.')}`;
    }
    path.unshift(selector);
    element = element.parentNode;
  }
  return path.join(' > ');
}

// 添加事件监听器的辅助函数
function addPassiveEventListener(element, eventType, handler) {
  element.addEventListener(eventType, handler, { passive: true });
}

// 修改点击按钮函数，移除验证逻辑
async function clickButton(button, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const scrollOptions = {
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      };

      await new Promise(resolve => {
        requestAnimationFrame(() => {
          button.scrollIntoView(scrollOptions);
          setTimeout(resolve, 500);
        });
      });

      const eventOptions = {
        bubbles: true,
        cancelable: true,
        view: window,
        passive: true
      };

      // 确保按钮可见且可点击
      const style = window.getComputedStyle(button);
      if (style.display === 'none' || style.visibility === 'hidden') {
        throw new Error('按钮不可见');
      }

      const events = ['mouseenter', 'mousedown', 'mouseup', 'click'];
      for (const eventType of events) {
        const event = new MouseEvent(eventType, eventOptions);
        button.dispatchEvent(event);
        await wait(100);
      }
      return;

    } catch (error) {
      if (i === retries - 1) {
        throw error;
      }
      await wait(1); // 最小重试间隔
    }
  }
}

// 辅助函数：等待元素出现
async function waitForElement(selector, timeout = 3000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element && element.offsetParent !== null) { // 确保元素可见
      return element;
    }
    await wait(100);
  }
  return null;
}

// 处理选择框的通用函数
async function handleSelect(type, selector) {
  try {
    Statistics.addLog(`检查${type}选择框`);
    
    // 使用提供的选择器查找选择框
    const select = document.querySelector(selector);
    // 修改备用选择器，确保能找到正确的元素
    const altSelect = document.querySelector(`.el-dialog__body .el-select input[placeholder="请选择"]`);
    
    // 合并选择器检查
    const targetSelect = select || altSelect;
    if (!targetSelect) {
      Statistics.addLog(`未找到${type}选择框，可能不需要选择`, 'info');
      return true;
    }

    // 检查是否为"请选择"状态
    const input = targetSelect.querySelector('input[placeholder="请选择"]');
    if (!input || input.value !== '') {
      Statistics.addLog(`${type}已选择，无需操作`, 'info');
      return true;
    }

    // 点击选择框并等待下拉框
    targetSelect.click();
    // 智能检测下拉框是否展开
    await fastWaitForVisible('.el-select-dropdown:not([style*="display: none"])');
    
    // 查找可见的下拉框
    const visibleDropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
    if (!visibleDropdown) {
      Statistics.addLog('未找到可见的下拉框', 'warning');
      return false;
    }

    // 在可见下拉框中查找第一个可用选项
    const firstOption = visibleDropdown.querySelector('.el-select-dropdown__item:not(.is-disabled)');
    if (!firstOption) {
      Statistics.addLog(`未找到${type}可用选项`, 'warning');
      return false;
    }

    // 点击第一个选项
    firstOption.click();
    await wait(1); // 最小必要等待
    
    // 验证选择是否成功
    if (targetSelect.value === '请选择') {
      Statistics.addLog(`${type}选择可能未成功，重试一次`, 'warning');
      firstOption.click();
      await wait(500);
    }

    Statistics.addLog(`已选择${type}选项`);
    return true;

  } catch (error) {
    Statistics.addLog(`${type}选择失败: ${error.message}`, 'error');
    return false;
  }
}

// 必填选择框配置（按网页顺序）
const REQUIRED_FIELD_CONFIG = {
  // 按照网页上的顺序排列
  '线索是否有效': {
    options: ['有效线索', '待定'],
    defaultValue: '有效线索',
    required: true
  },
  '意向车系': {
    options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine'],
    defaultValue: 'Q5L',
    required: true
  },
  '预购日期': {
    type: 'date',
    required: true
  },
  '线索等级': {
    options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）'],
    defaultValue: 'B（30天内跟进）',
    required: true
  },
  '跟进状态': {
    options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效'],
    defaultValue: '再次待跟进',
    required: true
  },
  '跟进方式': {
    options: ['电话沟通', '微信交流', '面对面沟通'],
    defaultValue: '电话沟通',
    required: true
  },
  '计划跟进时间': {
    type: 'date',
    required: true
  }
};

// 智能表单自动填充函数
async function autoFillForm() {
  try {
    Statistics.addLog('🤖 开始智能表单填充');
    let filledCount = 0;

    // 等待表单加载
    await wait(getOperationWaitTime());

    // 只处理必填字段（带*号），完全跳过非必填选择框
    Statistics.addLog('📋 跳过所有非必填选择框，只处理带*号的必填字段');

    if (settings && settings.forceRequired) {
      Statistics.addLog('🔥 启用强制填充必填字段（强制模式：处理所有必填字段）');
      await forceHandleRequiredFields();
    } else {
      Statistics.addLog('🔍 智能检测模式：只处理空的必填字段');
      await forceHandleRequiredFields(); // 使用智能检测
    }

    filledCount = 1; // 文本输入算作1个字段

    // 注意：必填的日期字段（预购日期、计划跟进时间）已在上面处理
    // 这里只处理其他非必填的日期字段（如果需要的话）
    Statistics.addLog('📅 必填日期字段已处理，跳过其他日期字段');

    // 处理文本区域
    await fillTextAreas();

    Statistics.addLog(`✅ 表单填充完成，共填充 ${filledCount} 个字段`, 'success');
    return true;

  } catch (error) {
    Statistics.addLog(`❌ 表单填充失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制处理必填字段（带*号）- 使用精确路径
async function forceHandleRequiredFields() {
  try {
    // 使用全局设置变量
    if (settings && settings.forceRequired) {
      Statistics.addLog('🔥 开始强制处理必填字段（强制模式：处理所有字段）');
    } else {
      Statistics.addLog('🔍 开始智能检测必填字段（只处理空字段）');
    }

    // 使用精确的CSS选择器路径处理关键必填字段
    await handleRequiredFieldByPath();

  } catch (error) {
    Statistics.addLog(`处理必填字段失败: ${error.message}`, 'error');
  }
}

// 智能检测空的必填字段
async function detectEmptyRequiredFields(requiredFields) {
  const fieldsToProcess = [];

  // 使用全局设置变量
  const forceMode = settings && settings.forceRequired;

  for (const field of requiredFields) {
    try {
      const element = document.querySelector(field.path);
      if (!element) {
        Statistics.addLog(`⚠️ 未找到${field.name}元素，将尝试处理`);
        fieldsToProcess.push(field);
        continue;
      }

      let isEmpty = false;

      if (field.type === 'select') {
        // 检查选择框是否有值
        const selectInput = element.querySelector('input, .el-input__inner');
        if (selectInput) {
          const value = selectInput.value || selectInput.placeholder;
          isEmpty = !value || value.trim() === '' || value.includes('请选择') || value.includes('全部');

          // 特殊处理线索等级：如果不是30天就强制修改
          if (field.name === '线索等级') {
            const isNot30Days = !value.includes('30天内跟进');
            if (isEmpty || isNot30Days) {
              fieldsToProcess.push(field);
              if (isNot30Days && !isEmpty) {
                Statistics.addLog(`🔄 线索等级当前为"${value}"，将修改为30天内跟进`);
              }
            }
          } else {
            if (forceMode) {
              fieldsToProcess.push(field);
            } else {
              if (isEmpty) {
                fieldsToProcess.push(field);
              }
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      } else if (field.type === 'date') {
        // 检查日期字段是否有值
        const dateInput = element.querySelector('input');
        if (dateInput) {
          const value = dateInput.value;
          isEmpty = !value || value.trim() === '';

          if (forceMode) {
            fieldsToProcess.push(field);
          } else {
            if (isEmpty) {
              fieldsToProcess.push(field);
            }
          }
        } else {
          isEmpty = true;
          Statistics.addLog(`🔍 ${field.name}未找到输入框，视为空`);
          fieldsToProcess.push(field);
        }
      }

    } catch (error) {
      Statistics.addLog(`⚠️ 检测${field.name}时出错: ${error.message}，将尝试处理`);
      fieldsToProcess.push(field);
    }
  }

  return fieldsToProcess;
}

// 使用精确路径处理必填字段 - 智能检测版本
async function handleRequiredFieldByPath() {
  try {
    // 定义所有必填字段的配置（按网页顺序）
    const requiredFields = [
      {
        name: '线索是否有效',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > div > div > div",
        type: 'select',
        options: ['有效线索', '待定']
      },
      {
        name: '意向车系',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(2) > div:nth-child(2) > div > div > div",
        type: 'select',
        options: ['Q5L', 'A4L Limousine', 'A6L Limousine', 'Q3', 'A3 Limousine']
      },
      {
        name: '预购日期',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(3) > div:nth-child(1) > div > div > div.el-date-editor.el-input.el-input--small.el-input--prefix.el-input--suffix.el-date-editor--datetime",
        type: 'date'
      },
      {
        name: '线索等级',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(1) > div > div > div",
        type: 'select',
        options: ['B（30天内跟进）', 'A（7天内跟进）', 'H（2天内跟进）']
      },
      {
        name: '跟进状态',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(2) > div > div > div",
        type: 'select',
        options: ['再次待跟进', '有意向到店', '已到店交接', '申请无效']
      },
      {
        name: '跟进方式',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(7) > div:nth-child(3) > div > div > div",
        type: 'select',
        options: ['电话沟通', '微信交流', '面对面沟通']
      },
      {
        name: '计划跟进时间',
        path: "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(1) > div:nth-child(8) > div:nth-child(2) > div > div > div.el-date-editor.el-input.el-input--small.el-input--prefix.el-input--suffix.el-date-editor--datetime",
        type: 'date'
      }
    ];

    // 智能检测需要处理的字段
    const fieldsToProcess = await detectEmptyRequiredFields(requiredFields);

    if (fieldsToProcess.length === 0) {
      Statistics.addLog('✅ 所有必填字段都已有值，无需处理');
      return;
    }

    Statistics.addLog(`🔍 检测到 ${fieldsToProcess.length} 个必填字段需要处理: ${fieldsToProcess.map(f => f.name).join(', ')}`);

    // 逐个处理需要填充的字段
    for (const field of fieldsToProcess) {
      try {
    

        let success = false;
        if (field.type === 'select') {
          success = await handleSelectByPath(field.path, field.name, field.options);
        } else if (field.type === 'date') {
          success = await handleElementUIDatePicker(field.path, field.name);
        }

        if (success) {
          Statistics.addLog(`✅ ${field.name}处理成功`);
        }

        await wait(getOperationWaitTime());

      } catch (error) {
        Statistics.addLog(`❌ 处理${field.name}时出错: ${error.message}`, 'error');
      }
    }



  } catch (error) {
    Statistics.addLog(`精确路径处理失败: ${error.message}`, 'error');
  }
}

// 使用精确路径处理选择框
async function handleSelectByPath(cssPath, fieldName, expectedOptions) {
  try {
    const element = document.querySelector(cssPath);
    if (!element) {
      return false;
    }

    // 滚动到元素位置
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 查找选择框
    const selectBox = element.querySelector('.el-select') || element.closest('.el-select') || element;
    if (!selectBox) {
      Statistics.addLog(`❌ ${fieldName}中未找到选择框`);
      return false;
    }

    // 强制点击选择框

    selectBox.click();
    // 智能检测选项是否加载完成
    await fastWaitForCondition(() => {
      const dropdown = document.querySelector('.el-select-dropdown:not([style*="display: none"])');
      return dropdown && dropdown.querySelectorAll('.el-select-dropdown__item').length > 0;
    });

    // 查找下拉选项
    let dropdown = null;
    for (let i = 0; i < 5; i++) {
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1];
        break;
      }
      await wait(300);
    }

    if (!dropdown) {
      Statistics.addLog(`❌ ${fieldName}下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');


    if (options.length > 0) {
      // 查找匹配的选项
      let selectedOption = null;

      for (const expectedOption of expectedOptions) {
        selectedOption = Array.from(options).find(opt =>
          opt.textContent.trim() === expectedOption
        );
        if (selectedOption) break;
      }

      // 如果没找到预期选项，选择第一个非"全部"的选项
      if (!selectedOption) {
        selectedOption = Array.from(options).find(opt => {
          const text = opt.textContent.trim();
          return text !== '' && text !== '全部' && text !== '请选择';
        });
      }

      if (selectedOption) {
        selectedOption.click();
        await wait(getOperationWaitTime());

        // 关闭下拉框
        document.body.click();
        await wait(getOperationWaitTime());

        Statistics.addLog(`✅ ${fieldName}设置成功`);
        return true;
      }
    }

    Statistics.addLog(`❌ ${fieldName}未找到合适选项`);
    document.body.click(); // 关闭下拉框
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 使用精确路径处理日期字段 - 模拟真实用户交互
async function handleDateByPath(cssPath, fieldName) {
  try {
    Statistics.addLog(`🔍 查找${fieldName}日期编辑器: ${cssPath}`);

    // 直接定位到el-date-editor元素
    const dateEditor = document.querySelector(cssPath);
    if (!dateEditor) {
      Statistics.addLog(`❌ 未找到${fieldName}日期编辑器`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}日期编辑器`);

    // 滚动到元素位置
    dateEditor.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(500);

    // 查找日期编辑器内的输入框
    const dateInput = dateEditor.querySelector('input') || dateEditor.querySelector('.el-input__inner');
    if (!dateInput) {
      Statistics.addLog(`❌ ${fieldName}日期编辑器中未找到输入框`);
      return false;
    }

    Statistics.addLog(`✅ 找到${fieldName}输入框`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(10 + Math.floor(Math.random() * 8));
    futureDate.setMinutes(Math.floor(Math.random() * 60));

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备通过模拟交互设置${fieldName}: ${dateString}`);

    // 模拟真实用户交互：点击输入框打开日期选择器
    Statistics.addLog(`📅 点击${fieldName}输入框打开日期选择器`);

    // 多次尝试点击，确保日期选择器打开
    for (let clickAttempt = 1; clickAttempt <= 3; clickAttempt++) {
      dateInput.click();
      // 智能检测日期选择器是否打开
      const opened = await fastWaitForVisible('.el-picker-panel:not([style*="display: none"])');
      if (opened) break;

      // 检查日期选择器是否打开
      const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      Statistics.addLog(`📅 第${clickAttempt}次点击后找到 ${panels.length} 个日期选择器面板`);

      if (panels.length > 0) {
        Statistics.addLog(`✅ 日期选择器已打开`);
        break;
      }

      if (clickAttempt < 3) {
        Statistics.addLog(`⚠️ 日期选择器未打开，重试点击`);
      }
    }

    // 查找并点击"此刻"按钮
    const nowButtonSelector = "body > div.el-picker-panel.el-date-picker.el-popper.has-time > div.el-picker-panel__footer > button.el-button.el-picker-panel__link-btn.el-button--text.el-button--mini";

    for (let attempt = 1; attempt <= 5; attempt++) {
      try {
        Statistics.addLog(`📅 第${attempt}次尝试查找"此刻"按钮`);

        // 智能检测"此刻"按钮是否可用
        await fastWaitForVisible('.el-picker-panel__footer button');

        // 调试：查找所有可能的日期选择器面板
        const allPanels = document.querySelectorAll('.el-picker-panel');
        Statistics.addLog(`🔍 找到 ${allPanels.length} 个日期选择器面板`);

        // 调试：查找所有可能的按钮
        const allButtons = document.querySelectorAll('.el-picker-panel__footer button');
        Statistics.addLog(`🔍 找到 ${allButtons.length} 个日期选择器按钮`);

        if (allButtons.length > 0) {
          const buttonTexts = Array.from(allButtons).map(btn => btn.textContent.trim());
          Statistics.addLog(`🔍 按钮文本: ${buttonTexts.join(', ')}`);
        }

        const nowButton = document.querySelector(nowButtonSelector);
        if (nowButton && nowButton.offsetParent !== null) { // 确保按钮可见
          Statistics.addLog(`✅ 找到"此刻"按钮，准备点击`);
          nowButton.click();
          // 智能检测日期是否已设置
          await fastWaitForCondition(() => {
            const input = document.querySelector(`${cssPath} input`);
            return input && input.value && input.value.trim() !== '';
          });

          // 查找输入框验证是否设置成功
          const dateInput = document.querySelector(`${cssPath} input`) ||
                           document.querySelector(`${cssPath} .el-input__inner`) ||
                           dateEditor.querySelector('input') ||
                           dateEditor.querySelector('.el-input__inner');

          if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过"此刻"按钮设置成功: ${dateInput.value}`);

            // 点击页面其他地方关闭日期选择器
            document.body.click();
            await wait(300);

            return true;
          } else {
            Statistics.addLog(`⚠️ ${fieldName}点击"此刻"按钮后未检测到值变化`);
          }
        } else {
          Statistics.addLog(`❌ 第${attempt}次未找到"此刻"按钮或按钮不可见`);

          // 尝试查找其他可能的"此刻"按钮选择器
          const alternativeSelectors = [
            "button:contains('此刻')",
            ".el-picker-panel__footer button",
            ".el-button--text:contains('此刻')",
            ".el-picker-panel__link-btn"
          ];

          for (const selector of alternativeSelectors) {
            try {
              const buttons = document.querySelectorAll(selector);
              for (const btn of buttons) {
                if (btn.textContent.includes('此刻') || btn.textContent.includes('现在')) {
                  Statistics.addLog(`✅ 通过备用选择器找到"此刻"按钮: ${btn.textContent.trim()}`);
                  btn.click();
                  await wait(800);

                  const dateInput = document.querySelector(`${cssPath} input`) ||
                                   document.querySelector(`${cssPath} .el-input__inner`);

                  if (dateInput && dateInput.value && dateInput.value.trim() !== '') {
                    Statistics.addLog(`✅ ${fieldName}通过备用按钮设置成功: ${dateInput.value}`);
                    document.body.click();
                    await wait(300);
                    return true;
                  }
                }
              }
            } catch (e) {
              // 忽略选择器错误
            }
          }

          // 如果找不到"此刻"按钮，可能需要重新点击输入框
          if (attempt <= 2) {
            Statistics.addLog(`🔄 重新点击${fieldName}输入框`);
            dateInput.click();
            await wait(1000);
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName}第${attempt}次操作失败: ${setError.message}`);
      }

      if (attempt < 5) {
        await wait(500);
      }
    }

    Statistics.addLog(`❌ ${fieldName}5次尝试均失败`);

    // 确保关闭任何打开的日期选择器
    try {
      document.body.click();
    } catch (e) {}

    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 根据标签文本查找表单项
async function findFormItemByLabel(labelText) {
  const labels = document.querySelectorAll('label.el-form-item__label');
  for (const label of labels) {
    if (label.textContent.trim() === labelText) {
      return label.closest('.el-form-item');
    }
  }
  return null;
}

// 强制选择特定选项
async function forceSelectSpecificOption(fieldName, selectBox, fieldConfig) {
  try {
    // 关闭所有打开的下拉框
    const openDropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
    for (const dropdown of openDropdowns) {
      dropdown.style.display = 'none';
    }
    await wait(300);

    // 移除滚动操作，直接处理

    // 特殊处理线索等级字段（可能需要动态加载选项）
    if (fieldName === '线索等级') {
      return await handleLevelSelect(selectBox);
    }

    // 点击选择框
    selectBox.click();
    await wait(getOperationWaitTime());

    // 等待下拉框出现（优化版）
    let dropdown = null;
    for (let i = 0; i < 3; i++) { // 减少重试次数
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        dropdown = dropdowns[dropdowns.length - 1]; // 取最后一个（最新打开的）
        break;
      }
      await wait(getOperationWaitTime());
    }

    if (!dropdown) {
      Statistics.addLog(`${fieldName} 下拉框未出现`);
      return false;
    }

    const options = dropdown.querySelectorAll('.el-select-dropdown__item');
    Statistics.addLog(`${fieldName} 找到 ${options.length} 个选项`);

    // 查找匹配的选项
    let selectedOption = null;

    // 首先尝试默认选项
    if (fieldConfig.defaultOption) {
      selectedOption = Array.from(options).find(opt =>
        opt.textContent.trim() === fieldConfig.defaultOption
      );
    }

    // 如果没找到默认选项，尝试其他预期选项
    if (!selectedOption && fieldConfig.expectedOptions) {
      for (const expectedOption of fieldConfig.expectedOptions) {
        selectedOption = Array.from(options).find(opt =>
          opt.textContent.trim() === expectedOption
        );
        if (selectedOption) break;
      }
    }

    if (selectedOption) {
      selectedOption.click();
      await wait(getOperationWaitTime());

      // 确保下拉框关闭
      document.body.click();
      await wait(getOperationWaitTime());

      Statistics.addLog(`✅ ${fieldName}设置成功`);
      return true;
    } else {
      // 关闭下拉框
      document.body.click();
      await wait(100); // 减少等待时间
      return false;
    }

  } catch (error) {
    Statistics.addLog(`${fieldName} 选择失败: ${error.message}`, 'error');
    try {
      document.body.click();
    } catch (e) {}
    return false;
  }
}

// 特殊处理线索等级选择
async function handleLevelSelect(selectBox) {
  try {
    Statistics.addLog(`🎯 特殊处理线索等级字段`);

    // 多次点击尝试激活选择框
    for (let attempt = 1; attempt <= 3; attempt++) {
      Statistics.addLog(`🖱️ 线索等级第${attempt}次点击尝试`);

      selectBox.click();
      await wait(getOperationWaitTime() * 2);

      // 查找下拉框
      const dropdowns = document.querySelectorAll('.el-select-dropdown:not([style*="display: none"])');
      if (dropdowns.length > 0) {
        const dropdown = dropdowns[dropdowns.length - 1];
        const options = dropdown.querySelectorAll('.el-select-dropdown__item');

        Statistics.addLog(`线索等级找到 ${options.length} 个选项`);

        if (options.length > 0) {
          // 记录所有选项
          const allOptions = Array.from(options).map(opt => opt.textContent.trim());
          Statistics.addLog(`线索等级可用选项: ${allOptions.join(', ')}`);

          // 查找合适的选项
          let selectedOption = null;

          // 优先查找包含"A"和"7天"的选项
          selectedOption = Array.from(options).find(opt => {
            const text = opt.textContent.trim();
            return text.includes('A') && text.includes('7天');
          });

          // 如果没找到，查找包含"A"的选项
          if (!selectedOption) {
            selectedOption = Array.from(options).find(opt => {
              const text = opt.textContent.trim();
              return text.includes('A（') || text.startsWith('A');
            });
          }

          // 如果还没找到，选择第一个非空选项
          if (!selectedOption) {
            selectedOption = Array.from(options).find(opt => {
              const text = opt.textContent.trim();
              return text !== '' && text !== '请选择' && text !== '全部';
            });
          }

          if (selectedOption) {
            Statistics.addLog(`🎯 线索等级选择: ${selectedOption.textContent.trim()}`);
            selectedOption.click();
            await wait(500);

            // 关闭下拉框
            document.body.click();
            await wait(300);

            Statistics.addLog(`✅ 线索等级选择成功: ${selectedOption.textContent.trim()}`);
            return true;
          }
        }
      }

      // 如果这次尝试失败，等待更长时间再试
      if (attempt < 3) {
        await wait(1000);
      }
    }

    Statistics.addLog(`❌ 线索等级3次尝试均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`线索等级处理失败: ${error.message}`, 'error');
    return false;
  }
}



// 强制填充日期
async function forceFillDate(fieldName, dateEditor) {
  try {
    Statistics.addLog(`📅 开始填充日期字段: ${fieldName}`);

    const input = dateEditor.querySelector('input');
    if (!input) {
      Statistics.addLog(`❌ ${fieldName} 未找到日期输入框`);
      return false;
    }

    // 检查是否已有值
    if (input.value && input.value.trim() !== '') {
      Statistics.addLog(`📅 ${fieldName} 已有值: ${input.value}，强制重新填充`);
    }

    // 滚动到位置
    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await wait(400);

    // 多次尝试激活输入框
    for (let i = 0; i < 3; i++) {
      input.click();
      await wait(300);
      input.focus();
      await wait(200);
    }

    let dateString = '';
    if (fieldName === '预购日期') {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
      futureDate.setHours(10 + Math.floor(Math.random() * 8));
      futureDate.setMinutes(Math.floor(Math.random() * 60));

      const year = futureDate.getFullYear();
      const month = String(futureDate.getMonth() + 1).padStart(2, '0');
      const day = String(futureDate.getDate()).padStart(2, '0');
      const hour = String(futureDate.getHours()).padStart(2, '0');
      const minute = String(futureDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    } else if (fieldName === '计划跟进时间') {
      const followDate = new Date();
      followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
      followDate.setHours(9 + Math.floor(Math.random() * 9));
      followDate.setMinutes(Math.floor(Math.random() * 60));

      const year = followDate.getFullYear();
      const month = String(followDate.getMonth() + 1).padStart(2, '0');
      const day = String(followDate.getDate()).padStart(2, '0');
      const hour = String(followDate.getHours()).padStart(2, '0');
      const minute = String(followDate.getMinutes()).padStart(2, '0');
      dateString = `${year}-${month}-${day} ${hour}:${minute}`;
    }

    if (dateString) {
      Statistics.addLog(`📅 准备设置日期: ${dateString}`);

      // 多种方法尝试设置值
      try {
        // 方法1: 直接设置值
        input.value = '';
        await wait(100);
        input.value = dateString;

        // 方法2: 模拟用户输入
        input.dispatchEvent(new Event('focus', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('input', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('change', { bubbles: true }));
        await wait(100);
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        // 方法3: 触发Vue事件（如果是Vue组件）
        if (input.__vue__) {
          input.__vue__.$emit('input', dateString);
          input.__vue__.$emit('change', dateString);
        }

        await wait(300);

        // 验证是否设置成功
        if (input.value === dateString) {
          Statistics.addLog(`✅ ${fieldName} 日期设置成功: ${dateString}`);
          return true;
        } else {
          Statistics.addLog(`⚠️ ${fieldName} 日期设置后验证失败，当前值: ${input.value}`);

          // 再次尝试设置
          input.value = dateString;
          input.dispatchEvent(new Event('input', { bubbles: true }));
          input.dispatchEvent(new Event('change', { bubbles: true }));
          await wait(200);

          if (input.value === dateString) {
            Statistics.addLog(`✅ ${fieldName} 第二次尝试成功: ${dateString}`);
            return true;
          }
        }

      } catch (setError) {
        Statistics.addLog(`❌ ${fieldName} 设置日期时出错: ${setError.message}`);
      }
    }

    Statistics.addLog(`❌ ${fieldName} 日期填充失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ 填充日期${fieldName}失败: ${error.message}`, 'error');
    return false;
  }
}

// 填充日期字段
async function fillDateFields() {
  try {
    Statistics.addLog('📅 开始填充日期字段');

    // 查找所有日期输入框
    const dateFields = document.querySelectorAll('.el-date-editor input');
    Statistics.addLog(`📅 找到 ${dateFields.length} 个日期字段`);

    for (const dateField of dateFields) {
      if (dateField.value && dateField.value.trim() !== '') {
        Statistics.addLog(`📅 日期字段已有值，跳过: ${dateField.value}`);
        continue;
      }

      const formItem = dateField.closest('.el-form-item');
      const label = formItem?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (!fieldName) {
        Statistics.addLog('📅 未找到日期字段标签');
        continue;
      }

      // 滚动到字段位置
      dateField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await wait(300);

      if (fieldName === '预购日期') {
        // 设置为未来7-30天的随机日期
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
        futureDate.setHours(10 + Math.floor(Math.random() * 8)); // 10-17点
        futureDate.setMinutes(Math.floor(Math.random() * 60));

        const year = futureDate.getFullYear();
        const month = String(futureDate.getMonth() + 1).padStart(2, '0');
        const day = String(futureDate.getDate()).padStart(2, '0');
        const hour = String(futureDate.getHours()).padStart(2, '0');
        const minute = String(futureDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);

      } else if (fieldName === '计划跟进时间') {
        // 设置为未来1-3天的随机时间
        const followDate = new Date();
        followDate.setDate(followDate.getDate() + Math.floor(Math.random() * 3) + 1);
        followDate.setHours(9 + Math.floor(Math.random() * 9)); // 9-17点
        followDate.setMinutes(Math.floor(Math.random() * 60));

        const year = followDate.getFullYear();
        const month = String(followDate.getMonth() + 1).padStart(2, '0');
        const day = String(followDate.getDate()).padStart(2, '0');
        const hour = String(followDate.getHours()).padStart(2, '0');
        const minute = String(followDate.getMinutes()).padStart(2, '0');
        const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

        // 点击输入框激活
        dateField.click();
        await wait(200);

        // 设置值
        dateField.value = dateString;
        dateField.dispatchEvent(new Event('input', { bubbles: true }));
        dateField.dispatchEvent(new Event('change', { bubbles: true }));
        dateField.dispatchEvent(new Event('blur', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${dateString}`);
        await wait(300);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充日期字段失败: ${error.message}`, 'error');
  }
}

// 填充文本区域
async function fillTextAreas() {
  try {
    const textAreas = document.querySelectorAll('textarea');

    for (const textArea of textAreas) {
      if (textArea.value) continue; // 跳过已有值的字段

      const label = textArea.closest('.el-form-item')?.querySelector('label');
      const fieldName = label?.textContent.trim();

      if (fieldName === '跟进说明') {
        const remarks = [
          '客户对车型表现出浓厚兴趣，计划近期到店详细了解',
          '已向客户介绍车型基本信息，客户反馈良好',
          '客户询问了优惠政策和金融方案，需要进一步跟进',
          '客户表示需要和家人商量，约定下次联系时间',
          '已发送车型详细资料给客户，等待客户反馈'
        ];

        const randomRemark = remarks[Math.floor(Math.random() * remarks.length)];
        textArea.value = randomRemark;
        textArea.dispatchEvent(new Event('input', { bubbles: true }));
        textArea.dispatchEvent(new Event('change', { bubbles: true }));

        Statistics.addLog(`✓ ${fieldName}: ${randomRemark}`);
        await wait(200);
      }
    }
  } catch (error) {
    Statistics.addLog(`填充文本区域失败: ${error.message}`, 'error');
  }
}

// 处理车型选择框
async function handleCarTypeSelect() {
  return handleSelect('车型', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(2) > div:nth-child(1) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 处理真实性选择框
async function handleRealitySelect() {
  return handleSelect('真实性', "#app > section > section > main > section > div > div:nth-child(7) > div > div.el-dialog__body > div > div.this-page > div.old-data-box > div.info > form > div:nth-child(4) > div:nth-child(4) > div > div > div > div.el-input.el-input--small.el-input--suffix");
}

// 专门处理Element UI日期选择器的函数
async function handleElementUIDatePicker(cssPath, fieldName) {
  try {
    const dateEditor = document.querySelector(cssPath);
    if (!dateEditor) {
      return false;
    }

    // 查找输入框
    const dateInput = dateEditor.querySelector('input');
    if (!dateInput) {
      return false;
    }

    // 首先尝试多种方式打开日期选择器
    const pickerOpened = await tryOpenDatePicker(dateEditor, dateInput, fieldName);
    if (!pickerOpened) {
      Statistics.addLog(`❌ ${fieldName}无法打开日期选择器，尝试简单设置`);
      return await trySimpleDateSet(dateInput, fieldName);
    }

    // 方法1: 尝试通过"此刻"按钮设置（最可靠）
    const nowButtonSuccess = await tryNowButton(dateInput, fieldName);
    if (nowButtonSuccess) {
      return true;
    }

    // 方法2: 尝试通过日期选择器面板设置
    const panelSuccess = await tryDatePickerPanel(dateInput, fieldName);
    if (panelSuccess) {
      return true;
    }

    // 方法3: 尝试简单设置（最后手段）
    const simpleSuccess = await trySimpleDateSet(dateInput, fieldName);
    if (simpleSuccess) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`${fieldName}处理失败: ${error.message}`, 'error');
    return false;
  }
}

// 尝试通过"此刻"按钮设置日期
async function tryNowButton(dateInput, fieldName) {
  try {


    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime());

    // 查找"此刻"按钮
    const nowButtons = document.querySelectorAll('.el-picker-panel__footer button');

    if (nowButtons.length > 0) {
      const nowButton = Array.from(nowButtons).find(btn =>
        btn.textContent.includes('此刻') || btn.textContent.includes('现在')
      );

      if (nowButton) {
        nowButton.click();
        await wait(getOperationWaitTime());

        // 验证设置结果
        if (dateInput.value && dateInput.value.trim() !== '') {
          Statistics.addLog(`✅ ${fieldName}设置成功: ${dateInput.value}`);

          // 关闭日期选择器
          document.body.click();
          await wait(getOperationWaitTime());

          return true;
        }
      }
    }

    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}"此刻"按钮方法出错: ${error.message}`);
    return false;
  }
}

// 尝试通过日期选择器面板设置
async function tryDatePickerPanel(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法2: 尝试通过日期选择器面板设置${fieldName}`);

    // 点击输入框打开日期选择器
    dateInput.click();
    await wait(getOperationWaitTime() * 2);

    // 查找日期选择器面板
    const panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length === 0) {
      Statistics.addLog(`❌ ${fieldName}日期选择器面板未打开`);
      return false;
    }

    Statistics.addLog(`✅ 找到 ${panels.length} 个日期选择器面板`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);

    // 尝试点击对应的日期
    const targetDay = futureDate.getDate();
    const dayButtons = document.querySelectorAll('.el-date-table td .cell');

    for (const dayButton of dayButtons) {
      if (dayButton.textContent.trim() === String(targetDay)) {
        Statistics.addLog(`📅 点击日期: ${targetDay}号`);
        dayButton.click();
        await wait(500);

        // 如果是日期时间选择器，还需要设置时间
        const timeInputs = document.querySelectorAll('.el-time-spinner input');
        if (timeInputs.length >= 2) {
          // 设置小时
          timeInputs[0].value = '14';
          timeInputs[0].dispatchEvent(new Event('input', { bubbles: true }));

          // 设置分钟
          timeInputs[1].value = '30';
          timeInputs[1].dispatchEvent(new Event('input', { bubbles: true }));

          await wait(300);
        }

        // 点击确定按钮
        const confirmButtons = document.querySelectorAll('.el-picker-panel__footer button');
        const confirmButton = Array.from(confirmButtons).find(btn =>
          btn.textContent.includes('确定') || btn.textContent.includes('确认')
        );

        if (confirmButton) {
          confirmButton.click();
          await wait(500);

          // 验证设置结果
          if (dateInput.value && dateInput.value.trim() !== '') {
            Statistics.addLog(`✅ ${fieldName}通过日期面板设置成功: ${dateInput.value}`);
            return true;
          }
        }

        break;
      }
    }

    Statistics.addLog(`❌ ${fieldName}日期面板方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}日期面板方法出错: ${error.message}`);
    return false;
  }
}

// 尝试键盘输入
async function tryKeyboardInput(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 方法3: 尝试键盘输入设置${fieldName}`);

    // 生成未来日期字符串
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 准备键盘输入日期: ${dateString}`);

    // 聚焦输入框
    dateInput.focus();
    await wait(200);

    // 清空输入框
    dateInput.select();
    await wait(100);

    // 模拟键盘输入（添加超时保护）
    dateInput.value = '';
    let charIndex = 0;
    for (const char of dateString) {
      if (charIndex > 20) { // 防止无限循环
        Statistics.addLog(`⚠️ 键盘输入超过20个字符，停止输入`);
        break;
      }

      dateInput.value += char;
      dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keypress', { key: char, bubbles: true }));
      dateInput.dispatchEvent(new Event('input', { bubbles: true }));
      dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: char, bubbles: true }));
      await wait(getOperationWaitTime() / 2);
      charIndex++;
    }

    // 触发change和blur事件
    dateInput.dispatchEvent(new Event('change', { bubbles: true }));
    await wait(100);
    dateInput.blur();
    dateInput.dispatchEvent(new Event('blur', { bubbles: true }));
    await wait(500);

    // 验证设置结果
    if (dateInput.value && dateInput.value.includes(dateString.split(' ')[0])) {
      Statistics.addLog(`✅ ${fieldName}通过键盘输入设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}键盘输入方法失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}键盘输入方法出错: ${error.message}`);
    return false;
  }
}

// 尝试打开日期选择器
async function tryOpenDatePicker(dateEditor, dateInput, fieldName) {
  try {

    // 方法1: 点击输入框
    dateInput.click();
    await wait(getOperationWaitTime());

    let panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法2: 点击日期编辑器容器
    dateEditor.click();
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    // 方法3: 点击日期图标
    const dateIcon = dateEditor.querySelector('.el-input__icon, .el-icon-time, .el-icon-date');
    if (dateIcon) {
      dateIcon.click();
      await wait(getOperationWaitTime());

      panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
      if (panels.length > 0) {
        return true;
      }
    }

    // 方法4: 双击输入框
    dateInput.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
    await wait(getOperationWaitTime());

    panels = document.querySelectorAll('.el-picker-panel:not([style*="display: none"])');
    if (panels.length > 0) {
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}所有打开方法均失败`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}打开日期选择器出错: ${error.message}`);
    return false;
  }
}

// 尝试简单设置日期（不依赖日期选择器）
async function trySimpleDateSet(dateInput, fieldName) {
  try {
    Statistics.addLog(`📅 尝试简单设置${fieldName}（不打开选择器）`);

    // 生成未来日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + Math.floor(Math.random() * 23) + 7);
    futureDate.setHours(14);
    futureDate.setMinutes(30);

    const year = futureDate.getFullYear();
    const month = String(futureDate.getMonth() + 1).padStart(2, '0');
    const day = String(futureDate.getDate()).padStart(2, '0');
    const hour = String(futureDate.getHours()).padStart(2, '0');
    const minute = String(futureDate.getMinutes()).padStart(2, '0');
    const dateString = `${year}-${month}-${day} ${hour}:${minute}`;

    Statistics.addLog(`📅 设置日期为: ${dateString}`);

    // 聚焦并清空
    dateInput.focus();
    await wait(200);
    dateInput.select();
    await wait(100);

    // 直接设置值
    dateInput.value = dateString;

    // 触发所有可能的事件
    const events = ['focus', 'input', 'change', 'blur'];
    for (const eventType of events) {
      dateInput.dispatchEvent(new Event(eventType, { bubbles: true }));
      await wait(100);
    }

    // 模拟回车键确认
    dateInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    dateInput.dispatchEvent(new KeyboardEvent('keyup', { key: 'Enter', bubbles: true }));

    await wait(500);

    // 验证设置结果
    if (dateInput.value && (dateInput.value === dateString || dateInput.value.includes(dateString.split(' ')[0]))) {
      Statistics.addLog(`✅ ${fieldName}简单设置成功: ${dateInput.value}`);
      return true;
    }

    Statistics.addLog(`❌ ${fieldName}简单设置失败，当前值: ${dateInput.value}`);
    return false;

  } catch (error) {
    Statistics.addLog(`❌ ${fieldName}简单设置出错: ${error.message}`);
    return false;
  }
}
